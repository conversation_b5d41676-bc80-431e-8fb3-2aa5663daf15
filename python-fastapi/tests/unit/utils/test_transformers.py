"""
Tests for data transformation utilities.
"""

import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from app.models.domain.wishlist import Wishlist, WishlistItem
from app.schemas.requests.wishlist import CreateWishlistRequest, UpdateWishlistRequest
from app.schemas.responses.wishlist import WishlistResponse, WishlistSummaryResponse
from app.utils.transformers import (
    WishlistRequestTransformer,
    WishlistResponseTransformer,
    WishlistSummaryTransformer,
    ProductDataTransformer,
    WishlistTransformationService,
    DataValidationPipeline,
    TransformationError
)
from app.core.exceptions import ValidationError


class TestWishlistRequestTransformer:
    """Test wishlist request transformer."""
    
    def test_transform_create_request(self):
        """Test transforming create request to domain model."""
        transformer = WishlistRequestTransformer()
        
        request = CreateWishlistRequest(
            user_id="user_123",
            name="Test Wishlist",
            is_default=True,
            country="ae",
            language="en"
        )
        
        wishlist = transformer.transform(request)
        
        assert wishlist.user_id == "user_123"
        assert wishlist.name == "Test Wishlist"
        assert wishlist.is_default is True
        assert wishlist.is_public is False  # New wishlists are private by default
        assert wishlist.wishlist_id is not None
        assert wishlist.share_hash is not None
        assert len(wishlist.share_hash) >= 16
        assert wishlist.items == []
    
    def test_reverse_transform(self):
        """Test reverse transformation from domain to request."""
        transformer = WishlistRequestTransformer()
        
        wishlist = Wishlist.create_new(
            user_id="user_123",
            name="Test Wishlist",
            is_default=True
        )
        
        request = transformer.reverse_transform(wishlist)
        
        assert request.user_id == "user_123"
        assert request.name == "Test Wishlist"
        assert request.is_default is True
        assert request.country == "ae"
        assert request.language == "en"


class TestWishlistResponseTransformer:
    """Test wishlist response transformer."""
    
    def test_transform_with_items(self):
        """Test transforming domain model to response with items."""
        transformer = WishlistResponseTransformer(include_items=True)
        
        # Create wishlist with items
        wishlist = Wishlist.create_new(
            user_id="user_123",
            name="Test Wishlist"
        )
        wishlist.add_item("product_1", "Test notes")
        
        response = transformer.transform(wishlist)
        
        assert response.user_id == "user_123"
        assert response.name == "Test Wishlist"
        assert response.item_count == 1
        assert response.items is not None
        assert len(response.items) == 1
        assert response.items[0].product_id == "product_1"
        assert response.items[0].notes == "Test notes"
        assert response.share_url.endswith(wishlist.share_hash)
    
    def test_transform_without_items(self):
        """Test transforming domain model to response without items."""
        transformer = WishlistResponseTransformer(include_items=False)
        
        wishlist = Wishlist.create_new(
            user_id="user_123",
            name="Test Wishlist"
        )
        wishlist.add_item("product_1", "Test notes")
        
        response = transformer.transform(wishlist)
        
        assert response.user_id == "user_123"
        assert response.name == "Test Wishlist"
        assert response.item_count == 1
        assert response.items is None
    
    def test_transform_with_product_data(self):
        """Test transforming items with enriched product data."""
        transformer = WishlistResponseTransformer(include_items=True)
        
        # Create wishlist with enriched item
        wishlist = Wishlist.create_new(
            user_id="user_123",
            name="Test Wishlist"
        )
        item = WishlistItem(
            product_id="product_1",
            notes="Test notes",
            product={
                'product_id': 'product_1',
                'title': 'Test Product',
                'price': 99.99,
                'currency': 'AED'
            }
        )
        wishlist.items = [item]
        
        response = transformer.transform(wishlist)
        
        assert response.items[0].product is not None
        assert response.items[0].product.product_id == 'product_1'
        assert response.items[0].product.title == 'Test Product'
        assert response.items[0].product.price == 99.99


class TestWishlistSummaryTransformer:
    """Test wishlist summary transformer."""
    
    def test_transform_to_summary(self):
        """Test transforming domain model to summary response."""
        transformer = WishlistSummaryTransformer()
        
        wishlist = Wishlist.create_new(
            user_id="user_123",
            name="Test Wishlist"
        )
        wishlist.add_item("product_1")
        wishlist.add_item("product_2")
        
        summary = transformer.transform(wishlist)
        
        assert summary.user_id == "user_123"
        assert summary.name == "Test Wishlist"
        assert summary.item_count == 2
        assert summary.is_default is False
        assert summary.is_public is False
    
    def test_reverse_transform_summary(self):
        """Test reverse transforming summary to domain model."""
        transformer = WishlistSummaryTransformer()
        
        summary = WishlistSummaryResponse(
            user_id="user_123",
            wishlist_id="wishlist_456",
            name="Test Wishlist",
            is_default=True,
            is_public=False,
            item_count=5,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        wishlist = transformer.reverse_transform(summary)
        
        assert wishlist.user_id == "user_123"
        assert wishlist.wishlist_id == "wishlist_456"
        assert wishlist.name == "Test Wishlist"
        assert wishlist.is_default is True
        assert wishlist.share_hash == ""  # Not available in summary
        assert wishlist.items == []  # Not available in summary


class TestProductDataTransformer:
    """Test product data transformer."""
    
    def test_from_algolia_response(self):
        """Test transforming Algolia response to standard format."""
        algolia_data = {
            'objectID': 'product_123',
            'title': 'Test Product',
            'description': 'A great product',
            'price': 99.99,
            'currency': 'AED',
            'image_url': 'https://example.com/image.jpg',
            'brand': 'TestBrand',
            'category': 'Electronics',
            'availability': 'in_stock',
            'rating': 4.5,
            'review_count': 128,
            'url': 'https://example.com/product'
        }
        
        result = ProductDataTransformer.from_algolia_response(algolia_data)
        
        assert result['product_id'] == 'product_123'
        assert result['title'] == 'Test Product'
        assert result['price'] == 99.99
        assert result['currency'] == 'AED'
        assert result['brand'] == 'TestBrand'
        assert result['rating'] == 4.5
        assert result['review_count'] == 128
    
    def test_from_algolia_response_minimal(self):
        """Test transforming minimal Algolia response."""
        algolia_data = {
            'objectID': 'product_123',
            'name': 'Test Product'  # Using 'name' instead of 'title'
        }
        
        result = ProductDataTransformer.from_algolia_response(algolia_data)
        
        assert result['product_id'] == 'product_123'
        assert result['title'] == 'Test Product'
        assert result['price'] is None
        assert result['currency'] == 'AED'
    
    def test_from_algolia_response_error_handling(self):
        """Test error handling in Algolia transformation."""
        algolia_data = {
            'objectID': 'product_123',
            'price': 'invalid_price'  # Invalid price format
        }
        
        result = ProductDataTransformer.from_algolia_response(algolia_data)
        
        # Should still return basic data even with errors
        assert result['product_id'] == 'product_123'
        assert result['title'] == 'Unknown Product'
        assert result['currency'] == 'AED'
    
    def test_batch_from_algolia(self):
        """Test batch transformation from Algolia results."""
        algolia_results = [
            {
                'objectID': 'product_1',
                'title': 'Product 1',
                'price': 50.0
            },
            {
                'objectID': 'product_2',
                'title': 'Product 2',
                'price': 75.0
            }
        ]
        
        result = ProductDataTransformer.batch_from_algolia(algolia_results)
        
        assert len(result) == 2
        assert 'product_1' in result
        assert 'product_2' in result
        assert result['product_1']['title'] == 'Product 1'
        assert result['product_2']['price'] == 75.0


class TestDataValidationPipeline:
    """Test data validation pipeline."""
    
    def test_pipeline_with_validators(self):
        """Test pipeline with validation steps."""
        pipeline = DataValidationPipeline()
        
        def validate_positive(data):
            if data <= 0:
                raise ValidationError("Value must be positive")
            return data
        
        def validate_max_value(data):
            if data > 100:
                raise ValidationError("Value must not exceed 100")
            return data
        
        pipeline.add_validator(validate_positive)
        pipeline.add_validator(validate_max_value)
        
        # Valid data should pass
        result = pipeline.process(50)
        assert result == 50
        
        # Invalid data should raise ValidationError
        with pytest.raises(ValidationError) as exc_info:
            pipeline.process(-10)
        assert "Value must be positive" in str(exc_info.value)
        
        with pytest.raises(ValidationError) as exc_info:
            pipeline.process(150)
        assert "Value must not exceed 100" in str(exc_info.value)
    
    def test_pipeline_with_transformers(self):
        """Test pipeline with transformation steps."""
        pipeline = DataValidationPipeline()
        
        def double_value(data):
            return data * 2
        
        def add_ten(data):
            return data + 10
        
        pipeline.add_transformer(double_value)
        pipeline.add_transformer(add_ten)
        
        result = pipeline.process(5)
        assert result == 20  # (5 * 2) + 10
    
    def test_pipeline_mixed_steps(self):
        """Test pipeline with both validation and transformation steps."""
        pipeline = DataValidationPipeline()
        
        def validate_positive(data):
            if data <= 0:
                raise ValidationError("Value must be positive")
            return data
        
        def double_value(data):
            return data * 2
        
        pipeline.add_validator(validate_positive)
        pipeline.add_transformer(double_value)
        
        result = pipeline.process(5)
        assert result == 10
        
        with pytest.raises(ValidationError):
            pipeline.process(-5)


class TestWishlistTransformationService:
    """Test wishlist transformation service."""
    
    def test_create_request_to_domain(self):
        """Test transforming create request to domain model."""
        service = WishlistTransformationService()
        
        request = CreateWishlistRequest(
            user_id="user_123",
            name="Test Wishlist",
            is_default=True,
            country="ae",
            language="en"
        )
        
        wishlist = service.create_request_to_domain(request)
        
        assert wishlist.user_id == "user_123"
        assert wishlist.name == "Test Wishlist"
        assert wishlist.is_default is True
    
    def test_update_request_to_domain(self):
        """Test applying update request to existing domain model."""
        service = WishlistTransformationService()
        
        existing = Wishlist.create_new(
            user_id="user_123",
            name="Original Name",
            is_default=False
        )
        
        request = UpdateWishlistRequest(
            user_id="user_123",
            name="Updated Name",
            is_default=True,
            country="ae",
            language="en"
        )
        
        updated = service.update_request_to_domain(request, existing)
        
        assert updated.name == "Updated Name"
        assert updated.is_default is True
        assert updated.user_id == existing.user_id
        assert updated.wishlist_id == existing.wishlist_id
        assert updated.updated_at > existing.updated_at
    
    def test_enrich_with_products(self):
        """Test enriching wishlist with product data."""
        service = WishlistTransformationService()
        
        wishlist = Wishlist.create_new(
            user_id="user_123",
            name="Test Wishlist"
        )
        wishlist.add_item("product_1")
        wishlist.add_item("product_2")
        
        product_data = {
            'product_1': {
                'product_id': 'product_1',
                'title': 'Product 1',
                'price': 50.0
            },
            'product_2': {
                'product_id': 'product_2',
                'title': 'Product 2',
                'price': 75.0
            }
        }
        
        enriched = service.enrich_with_products(wishlist, product_data)
        
        assert len(enriched.items) == 2
        assert enriched.items[0].product is not None
        assert enriched.items[0].product['title'] == 'Product 1'
        assert enriched.items[1].product['title'] == 'Product 2'
    
    def test_validate_and_transform_create_request(self):
        """Test validation and transformation of create request."""
        service = WishlistTransformationService()
        
        # Valid request
        request = CreateWishlistRequest(
            user_id="user_123",
            name="Test Wishlist",
            is_default=False,
            country="ae",
            language="en"
        )
        
        wishlist = service.validate_and_transform_create_request(request)
        assert wishlist.user_id == "user_123"
        assert wishlist.name == "Test Wishlist"
        
        # Invalid request - empty user_id
        invalid_request = CreateWishlistRequest(
            user_id="",
            name="Test Wishlist",
            is_default=False,
            country="ae",
            language="en"
        )
        
        with pytest.raises(ValidationError) as exc_info:
            service.validate_and_transform_create_request(invalid_request)
        assert "user_id is required" in str(exc_info.value)
        
        # Invalid request - empty name
        invalid_request2 = CreateWishlistRequest(
            user_id="user_123",
            name="",
            is_default=False,
            country="ae",
            language="en"
        )
        
        with pytest.raises(ValidationError) as exc_info:
            service.validate_and_transform_create_request(invalid_request2)
        assert "name is required" in str(exc_info.value)