"""
Async Algolia service with connection pooling, retry logic, and circuit breaker.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
import json

import aiohttp
from aiohttp import ClientSession, ClientTimeout, ClientError
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    before_sleep_log
)

from app.config.settings import get_settings
from app.core.exceptions import ExternalServiceError, AlgoliaServiceError
from app.core.metrics import metrics
from app.repositories.cache_repo import CacheRepository
from app.utils.circuit_breaker import CircuitBreaker, empty_dict_fallback, cached_data_fallback

logger = logging.getLogger(__name__)


class AlgoliaIndexConfig:
    """Configuration for Algolia indexes by country and language."""
    
    # Index mapping based on Laravel configuration
    INDEX_MAPPING = {
        "local": {
            # UAE
            'ae': {
                'en': 'magento2_aws_live_en_products',
                'ar': 'magento2_aws_live_ar_products',
                'default': 'magento2_aws_live_en_products',
            },
            # Saudi Arabia
            'sa': {
                'en': 'magento2_aws_live_sa_en_products',
                'ar': 'magento2_aws_live_sa_ar_products',
                'default': 'magento2_aws_live_sa_ar_products',
            },
            # Kuwait
            'kw': {
                'en': 'magento2_aws_live_kw_en_products',
                'ar': 'magento2_aws_live_kw_ar_products',
                'default': 'magento2_aws_live_kw_ar_products',
            },
            # Bahrain
            'bh': {
                'en': 'magento2_aws_live_bh_en_products',
                'ar': 'magento2_aws_live_bh_ar_products',
                'default': 'magento2_aws_live_bh_ar_products',
            },
            # Qatar
            'qa': {
                'en': 'magento2_aws_live_qa_en_products',
                'ar': 'magento2_aws_live_qa_ar_products',
                'default': 'magento2_aws_live_qa_ar_products',
            },
            # Oman
            'om': {
                'en': 'magento2_aws_live_om_en_products',
                'ar': 'magento2_aws_live_om_ar_products',
                'default': 'magento2_aws_live_om_ar_products',
            },
            # Default fallback
            'default': {
                'en': 'magento2_aws_live_en_products',
                'ar': 'magento2_aws_live_ar_products',
                'default': 'magento2_aws_live_en_products',
            },
        }
    }
    
    @classmethod
    def get_index_name(cls, country: str, language: str, environment: str = "local") -> str:
        """Get the appropriate Algolia index name based on country and language."""
        try:
            # Try country-language specific index
            if environment in cls.INDEX_MAPPING:
                env_config = cls.INDEX_MAPPING[environment]
                if country in env_config and language in env_config[country]:
                    return env_config[country][language]
                
                # Fallback to country default
                if country in env_config and 'default' in env_config[country]:
                    return env_config[country]['default']
                
                # Fallback to global default
                if 'default' in env_config and language in env_config['default']:
                    return env_config['default'][language]
                
                # Final fallback
                if 'default' in env_config and 'default' in env_config['default']:
                    return env_config['default']['default']
            
            # Ultimate fallback
            return 'magento2_aws_live_en_products'
            
        except Exception as e:
            logger.error(f"Error getting index name: {e}")
            return 'magento2_aws_live_en_products'


class AlgoliaService:
    """
    Async Algolia service with connection pooling, retry logic, and circuit breaker.
    """
    
    def __init__(self, cache_repo: Optional[CacheRepository] = None):
        self.settings = get_settings()
        self.cache_repo = cache_repo
        self._session: Optional[ClientSession] = None
        # Create fallback function for Algolia service
        async def algolia_fallback(*args, **kwargs):
            """Fallback function that returns cached data or empty results."""
            logger.warning("Using Algolia fallback - service unavailable")
            
            # Try to return cached data if cache_repo is available
            if self.cache_repo and len(args) > 0:
                # For single product requests
                if len(args) == 3:  # product_id, country, language
                    product_id, country, language = args[:3]
                    cache_key = f"algolia:product:{product_id}:{country}:{language}"
                    cached_result = await self.cache_repo.get(cache_key)
                    if cached_result:
                        logger.info(f"Returning cached product data for {product_id} from fallback")
                        return cached_result
                
                # For multiple products requests
                elif len(args) == 3 and isinstance(args[0], list):  # product_ids, country, language
                    product_ids, country, language = args[:3]
                    cached_products = {}
                    for product_id in product_ids:
                        cache_key = f"algolia:product:{product_id}:{country}:{language}"
                        cached_result = await self.cache_repo.get(cache_key)
                        if cached_result:
                            cached_products[product_id] = cached_result
                    
                    if cached_products:
                        logger.info(f"Returning {len(cached_products)} cached products from fallback")
                        return cached_products
            
            # Return empty result as last resort
            if len(args) > 0 and isinstance(args[0], list):
                return {}  # Empty dict for multiple products
            else:
                return None  # None for single product
        
        self._circuit_breaker = CircuitBreaker(
            failure_threshold=self.settings.CIRCUIT_BREAKER_THRESHOLD,
            recovery_timeout=self.settings.CIRCUIT_BREAKER_TIMEOUT,
            expected_exception=(ClientError, asyncio.TimeoutError, AlgoliaServiceError),
            name="algolia_service",
            fallback_function=algolia_fallback,
            success_threshold=2,  # Need 2 successes to close from half-open
            timeout=self.settings.ALGOLIA_TIMEOUT
        )
        
        # Algolia API configuration
        self.app_id = self.settings.ALGOLIA_APP_ID
        self.api_key = self.settings.ALGOLIA_API_KEY
        self.base_url = f"https://{self.app_id}-dsn.algolia.net"
        
        # Connection configuration
        self.timeout = ClientTimeout(total=self.settings.ALGOLIA_TIMEOUT)
        self.max_retries = self.settings.ALGOLIA_MAX_RETRIES
        
        logger.info("AlgoliaService initialized", extra={
            "app_id": self.app_id,
            "timeout": self.settings.ALGOLIA_TIMEOUT,
            "max_retries": self.max_retries
        })
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    
    async def _ensure_session(self) -> ClientSession:
        """Ensure HTTP session is created with connection pooling."""
        if self._session is None or self._session.closed:
            connector = aiohttp.TCPConnector(
                limit=100,  # Total connection pool size
                limit_per_host=20,  # Per-host connection limit
                ttl_dns_cache=300,  # DNS cache TTL
                use_dns_cache=True,
                keepalive_timeout=30,
                enable_cleanup_closed=True
            )
            
            self._session = ClientSession(
                connector=connector,
                timeout=self.timeout,
                headers={
                    'X-Algolia-Application-Id': self.app_id,
                    'X-Algolia-API-Key': self.api_key,
                    'Content-Type': 'application/json',
                    'User-Agent': f'Mumzworld-Wishlist-Service/{self.settings.VERSION}'
                }
            )
            
            logger.debug("Created new Algolia HTTP session with connection pooling")
        
        return self._session
    
    async def close(self):
        """Close the HTTP session and cleanup resources."""
        if self._session and not self._session.closed:
            await self._session.close()
            logger.debug("Closed Algolia HTTP session")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        retry=retry_if_exception_type((ClientError, asyncio.TimeoutError)),
        before_sleep=before_sleep_log(logger, logging.WARNING)
    )
    async def _make_request(
        self, 
        method: str, 
        url: str, 
        **kwargs
    ) -> Dict[str, Any]:
        """Make HTTP request with retry logic."""
        session = await self._ensure_session()
        
        try:
            async with session.request(method, url, **kwargs) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    return {}  # Not found, return empty dict
                else:
                    error_text = await response.text()
                    raise AlgoliaServiceError(
                        f"Algolia API error: {response.status} - {error_text}"
                    )
        except asyncio.TimeoutError:
            logger.error(f"Timeout making request to {url}")
            raise
        except ClientError as e:
            logger.error(f"Client error making request to {url}: {e}")
            raise
    
    async def get_product_by_id(
        self, 
        product_id: str, 
        country: str = 'ae', 
        language: str = 'en'
    ) -> Optional[Dict[str, Any]]:
        """
        Get a single product by ID with caching and fallback logic.
        """
        # Check cache first
        cache_key = f"algolia:product:{product_id}:{country}:{language}"
        if self.cache_repo:
            cached_result = await self.cache_repo.get(cache_key)
            if cached_result:
                metrics.counter("algolia_cache_hit").inc()
                logger.debug(f"Cache hit for product {product_id}")
                return cached_result
        
        try:
            with metrics.timer("algolia_get_product_duration"):
                result = await self._circuit_breaker.call(
                    self._get_product_by_id_internal,
                    product_id,
                    country,
                    language
                )
                
                # Cache the result if successful
                if result and self.cache_repo:
                    await self.cache_repo.set(
                        cache_key, 
                        result, 
                        ttl=self.settings.CACHE_TTL_PRODUCTS
                    )
                    metrics.counter("algolia_cache_miss").inc()
                
                return result
                
        except Exception as e:
            logger.error(f"Error getting product {product_id}: {e}")
            metrics.counter("algolia_error", {"operation": "get_product"}).inc()
            
            # Return cached result if available during error
            if self.cache_repo:
                stale_result = await self.cache_repo.get(cache_key)
                if stale_result:
                    logger.info(f"Returning stale cached result for product {product_id}")
                    return stale_result
            
            return None
    
    async def _get_product_by_id_internal(
        self, 
        product_id: str, 
        country: str, 
        language: str
    ) -> Optional[Dict[str, Any]]:
        """Internal method to get product by ID with fallback logic."""
        # Try with specified language first
        try:
            index_name = AlgoliaIndexConfig.get_index_name(country, language, self.settings.ENVIRONMENT)
            url = f"{self.base_url}/1/indexes/{index_name}/{product_id}"
            
            logger.debug(f"Getting product {product_id} from index {index_name}")
            
            result = await self._make_request('GET', url)
            
            if result:
                logger.debug(f"Retrieved product {product_id} from Algolia")
                metrics.counter("algolia_success", {"operation": "get_product"}).inc()
                return result
            
        except Exception as e:
            logger.warning(f"Failed to get product {product_id} with language {language}: {e}")
        
        # Fallback to English if not already using English
        if language != 'en':
            try:
                logger.info(f"Attempting fallback to English for product {product_id}")
                return await self._get_product_by_id_internal(product_id, country, 'en')
            except Exception as e:
                logger.error(f"Fallback to English also failed for product {product_id}: {e}")
        
        return None
    
    async def get_products_by_ids(
        self, 
        product_ids: List[str], 
        country: str = 'ae', 
        language: str = 'en'
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get multiple products by IDs with batching, caching, and fallback logic.
        """
        if not product_ids:
            return {}
        
        # Check cache for each product
        cached_products = {}
        uncached_ids = []
        
        if self.cache_repo:
            for product_id in product_ids:
                cache_key = f"algolia:product:{product_id}:{country}:{language}"
                cached_result = await self.cache_repo.get(cache_key)
                if cached_result:
                    cached_products[product_id] = cached_result
                else:
                    uncached_ids.append(product_id)
        else:
            uncached_ids = product_ids
        
        if cached_products:
            metrics.counter("algolia_cache_hit", {"count": len(cached_products)}).inc()
            logger.debug(f"Cache hit for {len(cached_products)} products")
        
        # Fetch uncached products
        fetched_products = {}
        if uncached_ids:
            try:
                with metrics.timer("algolia_get_products_duration"):
                    fetched_products = await self._circuit_breaker.call(
                        self._get_products_by_ids_internal,
                        uncached_ids,
                        country,
                        language
                    )
                    
                    # Cache the fetched results
                    if fetched_products and self.cache_repo:
                        cache_tasks = []
                        for product_id, product_data in fetched_products.items():
                            cache_key = f"algolia:product:{product_id}:{country}:{language}"
                            cache_tasks.append(
                                self.cache_repo.set(
                                    cache_key, 
                                    product_data, 
                                    ttl=self.settings.CACHE_TTL_PRODUCTS
                                )
                            )
                        
                        if cache_tasks:
                            await asyncio.gather(*cache_tasks, return_exceptions=True)
                        
                        metrics.counter("algolia_cache_miss", {"count": len(fetched_products)}).inc()
                        
            except Exception as e:
                logger.error(f"Error getting products {uncached_ids}: {e}")
                metrics.counter("algolia_error", {"operation": "get_products"}).inc()
        
        # Combine cached and fetched results
        all_products = {**cached_products, **fetched_products}
        
        # Log missing products
        missing_products = set(product_ids) - set(all_products.keys())
        if missing_products:
            logger.warning(f"Products not found: {list(missing_products)}")
            metrics.counter("algolia_missing_products", {"count": len(missing_products)}).inc()
        
        return all_products
    
    async def _get_products_by_ids_internal(
        self, 
        product_ids: List[str], 
        country: str, 
        language: str
    ) -> Dict[str, Dict[str, Any]]:
        """Internal method to get multiple products with fallback logic."""
        # Try with specified language first
        try:
            index_name = AlgoliaIndexConfig.get_index_name(country, language, self.settings.ENVIRONMENT)
            
            # Prepare batch request
            requests = [
                {
                    "indexName": index_name,
                    "objectID": product_id
                }
                for product_id in product_ids
            ]
            
            url = f"{self.base_url}/1/indexes/*/objects"
            payload = {"requests": requests}
            
            logger.debug(f"Getting {len(product_ids)} products from index {index_name}")
            
            response = await self._make_request('POST', url, json=payload)
            
            # Process results
            products = {}
            if 'results' in response and isinstance(response['results'], list):
                for result in response['results']:
                    if result and 'objectID' in result:
                        products[result['objectID']] = result
            
            if products:
                logger.debug(f"Retrieved {len(products)} products from Algolia")
                metrics.counter("algolia_success", {"operation": "get_products"}).inc()
            
            return products
            
        except Exception as e:
            logger.warning(f"Failed to get products with language {language}: {e}")
        
        # Fallback to English if not already using English
        if language != 'en':
            try:
                logger.info(f"Attempting fallback to English for {len(product_ids)} products")
                return await self._get_products_by_ids_internal(product_ids, country, 'en')
            except Exception as e:
                logger.error(f"Fallback to English also failed for products: {e}")
        
        return {}
    
    async def product_exists(
        self, 
        product_id: str, 
        country: str = 'ae', 
        language: str = 'en'
    ) -> bool:
        """
        Check if a product exists in Algolia.
        """
        try:
            product = await self.get_product_by_id(product_id, country, language)
            return product is not None
        except Exception as e:
            logger.error(f"Error checking if product {product_id} exists: {e}")
            return False
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Perform health check on Algolia service.
        """
        try:
            # Try to get a simple object to test connectivity
            index_name = AlgoliaIndexConfig.get_index_name('ae', 'en', self.settings.ENVIRONMENT)
            url = f"{self.base_url}/1/indexes/{index_name}/settings"
            
            start_time = datetime.utcnow()
            await self._make_request('GET', url)
            response_time = (datetime.utcnow() - start_time).total_seconds()
            
            return {
                "status": "healthy",
                "response_time_seconds": response_time,
                "circuit_breaker_state": self._circuit_breaker.state,
                "app_id": self.app_id
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "circuit_breaker_state": self._circuit_breaker.state,
                "app_id": self.app_id
            }


# Singleton instance for dependency injection
_algolia_service: Optional[AlgoliaService] = None


async def get_algolia_service(cache_repo: Optional[CacheRepository] = None) -> AlgoliaService:
    """Get or create Algolia service instance."""
    global _algolia_service
    
    if _algolia_service is None:
        _algolia_service = AlgoliaService(cache_repo)
    
    return _algolia_service


async def close_algolia_service():
    """Close Algolia service and cleanup resources."""
    global _algolia_service
    
    if _algolia_service:
        await _algolia_service.close()
        _algolia_service = None