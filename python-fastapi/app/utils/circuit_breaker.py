"""
Circuit breaker pattern implementation for external service resilience.
"""
import asyncio
import logging
from datetime import datetime, timedelta, timezone
from enum import Enum
from typing import Any, Callable, Optional, Dict, Union, Awaitable
from functools import wraps
import inspect

from app.core.metrics import metrics

logger = logging.getLogger(__name__)


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, blocking requests
    HALF_OPEN = "half_open"  # Testing if service recovered


class CircuitBreakerError(Exception):
    """Raised when circuit breaker is open."""
    
    def __init__(self, message: str, circuit_name: str, state: str):
        super().__init__(message)
        self.circuit_name = circuit_name
        self.state = state
        self.timestamp = datetime.now(timezone.utc)


class FallbackError(Exception):
    """Raised when fallback mechanism fails."""
    
    def __init__(self, message: str, original_error: Exception, fallback_error: Exception):
        super().__init__(message)
        self.original_error = original_error
        self.fallback_error = fallback_error


class CircuitBreaker:
    """
    Circuit breaker implementation for external service calls.
    
    States:
    - CLOSED: Normal operation, requests pass through
    - OPEN: Service is failing, requests are blocked
    - HALF_OPEN: Testing if service has recovered
    """
    
    def __init__(
        self,
        failure_threshold: int = 5,
        recovery_timeout: int = 60,
        expected_exception: type = Exception,
        name: str = "circuit_breaker",
        fallback_function: Optional[Callable] = None,
        success_threshold: int = 1,  # Successes needed to close from half-open
        timeout: Optional[int] = None  # Request timeout
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.name = name
        self.fallback_function = fallback_function
        self.success_threshold = success_threshold
        self.timeout = timeout
        
        # State management
        self._state = CircuitBreakerState.CLOSED
        self._failure_count = 0
        self._success_count_in_half_open = 0
        self._last_failure_time: Optional[datetime] = None
        self._next_attempt_time: Optional[datetime] = None
        self._last_state_change: datetime = datetime.now(timezone.utc)
        
        # Metrics and monitoring
        self._success_count = 0
        self._total_requests = 0
        self._blocked_requests = 0
        self._fallback_calls = 0
        self._fallback_successes = 0
        self._response_times: list = []  # Keep last 100 response times
        self._max_response_times = 100
        
        # Register circuit breaker for monitoring
        register_circuit_breaker(self.name, self)
        
        logger.info(f"Circuit breaker '{name}' initialized", extra={
            "failure_threshold": failure_threshold,
            "recovery_timeout": recovery_timeout,
            "success_threshold": success_threshold,
            "has_fallback": fallback_function is not None,
            "timeout": timeout
        })
    
    @property
    def state(self) -> str:
        """Get current circuit breaker state."""
        return self._state.value
    
    @property
    def failure_count(self) -> int:
        """Get current failure count."""
        return self._failure_count
    
    @property
    def success_rate(self) -> float:
        """Get success rate percentage."""
        if self._total_requests == 0:
            return 100.0
        return (self._success_count / self._total_requests) * 100
    
    @property
    def average_response_time(self) -> float:
        """Get average response time in seconds."""
        if not self._response_times:
            return 0.0
        return sum(self._response_times) / len(self._response_times)
    
    @property
    def state_duration(self) -> float:
        """Get duration in current state (seconds)."""
        return (datetime.now(timezone.utc) - self._last_state_change).total_seconds()
    
    @property
    def has_fallback(self) -> bool:
        """Check if circuit breaker has a fallback function."""
        return self.fallback_function is not None
    
    def _should_attempt_reset(self) -> bool:
        """Check if we should attempt to reset the circuit breaker."""
        if self._state != CircuitBreakerState.OPEN:
            return False
        
        if self._next_attempt_time is None:
            return True
        
        return datetime.now(timezone.utc) >= self._next_attempt_time
    
    def _on_success(self, response_time: float = 0.0):
        """Handle successful request."""
        self._success_count += 1
        self._total_requests += 1
        
        # Track response time
        if response_time > 0:
            self._response_times.append(response_time)
            if len(self._response_times) > self._max_response_times:
                self._response_times.pop(0)
        
        # Handle state transitions
        if self._state == CircuitBreakerState.HALF_OPEN:
            self._success_count_in_half_open += 1
            
            if self._success_count_in_half_open >= self.success_threshold:
                self._change_state(CircuitBreakerState.CLOSED)
                self._failure_count = 0
                self._success_count_in_half_open = 0
                logger.info(f"Circuit breaker '{self.name}' reset to CLOSED after {self._success_count_in_half_open} successes")
                metrics.counter("circuit_breaker_closed", {"name": self.name}).inc()
        elif self._state == CircuitBreakerState.CLOSED:
            # Reset failure count on success in closed state
            self._failure_count = 0
    
    def _on_failure(self, exception: Exception):
        """Handle failed request."""
        self._failure_count += 1
        self._total_requests += 1
        self._last_failure_time = datetime.now(timezone.utc)
        
        logger.warning(f"Circuit breaker '{self.name}' failure {self._failure_count}/{self.failure_threshold}: {exception}")
        
        # Handle state transitions based on current state
        if self._state == CircuitBreakerState.CLOSED:
            if self._failure_count >= self.failure_threshold:
                self._change_state(CircuitBreakerState.OPEN)
                self._next_attempt_time = datetime.now(timezone.utc) + timedelta(seconds=self.recovery_timeout)
                
                logger.error(f"Circuit breaker '{self.name}' opened due to {self._failure_count} failures")
                metrics.counter("circuit_breaker_opened", {"name": self.name}).inc()
                
        elif self._state == CircuitBreakerState.HALF_OPEN:
            # Any failure in half-open state goes back to open
            self._change_state(CircuitBreakerState.OPEN)
            self._next_attempt_time = datetime.now(timezone.utc) + timedelta(seconds=self.recovery_timeout)
            self._success_count_in_half_open = 0
            
            logger.warning(f"Circuit breaker '{self.name}' returned to OPEN from HALF_OPEN due to failure")
            metrics.counter("circuit_breaker_opened", {"name": self.name}).inc()
    
    def _change_state(self, new_state: CircuitBreakerState):
        """Change circuit breaker state and update metrics."""
        old_state = self._state
        self._state = new_state
        self._last_state_change = datetime.now(timezone.utc)
        
        # Update state metrics
        metrics.gauge("circuit_breaker_state", {"name": self.name, "state": new_state.value}).set(1)
        metrics.gauge("circuit_breaker_state", {"name": self.name, "state": old_state.value}).set(0)
        
        logger.info(f"Circuit breaker '{self.name}' state changed: {old_state.value} -> {new_state.value}")
    
    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute function with circuit breaker protection and fallback support.
        """
        # Check if we should attempt reset
        if self._should_attempt_reset():
            self._change_state(CircuitBreakerState.HALF_OPEN)
            logger.info(f"Circuit breaker '{self.name}' moved to HALF_OPEN for testing")
        
        # Block requests if circuit is open
        if self._state == CircuitBreakerState.OPEN:
            self._blocked_requests += 1
            metrics.counter("circuit_breaker_blocked", {"name": self.name}).inc()
            
            # Try fallback if available
            if self.fallback_function:
                return await self._execute_fallback(*args, **kwargs)
            
            # No fallback, raise error
            error_msg = f"Circuit breaker '{self.name}' is OPEN"
            logger.warning(error_msg)
            raise CircuitBreakerError(error_msg, self.name, self._state.value)
        
        # Execute the function with timeout if specified
        start_time = datetime.now(timezone.utc)
        try:
            if self.timeout:
                if asyncio.iscoroutinefunction(func):
                    result = await asyncio.wait_for(func(*args, **kwargs), timeout=self.timeout)
                else:
                    # For sync functions, we can't apply timeout easily, so just call it
                    result = func(*args, **kwargs)
            else:
                if asyncio.iscoroutinefunction(func):
                    result = await func(*args, **kwargs)
                else:
                    result = func(*args, **kwargs)
            
            # Calculate response time
            response_time = (datetime.now(timezone.utc) - start_time).total_seconds()
            
            self._on_success(response_time)
            metrics.counter("circuit_breaker_success", {"name": self.name}).inc()
            metrics.histogram("circuit_breaker_response_time", {"name": self.name}).observe(response_time)
            
            return result
            
        except asyncio.TimeoutError as e:
            # Timeout counts as failure
            self._on_failure(e)
            metrics.counter("circuit_breaker_timeout", {"name": self.name}).inc()
            
            # Only use fallback on timeout if circuit is now open
            if self._state == CircuitBreakerState.OPEN and self.fallback_function:
                return await self._execute_fallback(*args, **kwargs)
            
            raise
            
        except self.expected_exception as e:
            self._on_failure(e)
            metrics.counter("circuit_breaker_failure", {"name": self.name}).inc()
            
            # Only use fallback if circuit is now open after this failure
            if self._state == CircuitBreakerState.OPEN and self.fallback_function:
                return await self._execute_fallback(*args, **kwargs)
            
            raise
            
        except Exception as e:
            # Unexpected exceptions don't count as failures but are still logged
            logger.error(f"Unexpected exception in circuit breaker '{self.name}': {e}")
            metrics.counter("circuit_breaker_unexpected_error", {"name": self.name}).inc()
            
            # Only use fallback for unexpected errors if circuit is open
            if self._state == CircuitBreakerState.OPEN and self.fallback_function:
                return await self._execute_fallback(*args, **kwargs)
            
            raise
    
    async def _execute_fallback(self, *args, **kwargs) -> Any:
        """Execute fallback function with error handling."""
        try:
            self._fallback_calls += 1
            metrics.counter("circuit_breaker_fallback_called", {"name": self.name}).inc()
            
            logger.info(f"Executing fallback for circuit breaker '{self.name}'")
            
            if asyncio.iscoroutinefunction(self.fallback_function):
                result = await self.fallback_function(*args, **kwargs)
            elif callable(self.fallback_function):
                # Check if it's a callable object (like FallbackChain)
                if hasattr(self.fallback_function, '__call__'):
                    result = self.fallback_function(*args, **kwargs)
                    # If the result is a coroutine, await it
                    if asyncio.iscoroutine(result):
                        result = await result
                else:
                    result = self.fallback_function(*args, **kwargs)
            else:
                result = self.fallback_function(*args, **kwargs)
            
            self._fallback_successes += 1
            metrics.counter("circuit_breaker_fallback_success", {"name": self.name}).inc()
            
            return result
            
        except Exception as fallback_error:
            logger.error(f"Fallback function failed for circuit breaker '{self.name}': {fallback_error}")
            metrics.counter("circuit_breaker_fallback_failure", {"name": self.name}).inc()
            
            # Return None or empty result as last resort, or re-raise
            # This depends on the specific use case
            raise fallback_error
    
    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive circuit breaker statistics."""
        return {
            "name": self.name,
            "state": self.state,
            "state_duration_seconds": self.state_duration,
            "failure_count": self._failure_count,
            "failure_threshold": self.failure_threshold,
            "success_count": self._success_count,
            "success_threshold": self.success_threshold,
            "success_count_in_half_open": self._success_count_in_half_open,
            "total_requests": self._total_requests,
            "blocked_requests": self._blocked_requests,
            "success_rate": round(self.success_rate, 2),
            "average_response_time": round(self.average_response_time, 3),
            "has_fallback": self.has_fallback,
            "fallback_calls": self._fallback_calls,
            "fallback_successes": self._fallback_successes,
            "fallback_success_rate": round(
                (self._fallback_successes / self._fallback_calls * 100) if self._fallback_calls > 0 else 0, 2
            ),
            "last_failure_time": self._last_failure_time.isoformat() if self._last_failure_time else None,
            "next_attempt_time": self._next_attempt_time.isoformat() if self._next_attempt_time else None,
            "last_state_change": self._last_state_change.isoformat(),
            "recovery_timeout": self.recovery_timeout,
            "timeout": self.timeout,
            "expected_exception": self.expected_exception.__name__ if hasattr(self.expected_exception, '__name__') else str(self.expected_exception)
        }
    
    def reset(self):
        """Manually reset the circuit breaker."""
        old_state = self._state
        self._change_state(CircuitBreakerState.CLOSED)
        self._failure_count = 0
        self._success_count_in_half_open = 0
        self._last_failure_time = None
        self._next_attempt_time = None
        
        logger.info(f"Circuit breaker '{self.name}' manually reset from {old_state.value}")
        metrics.counter("circuit_breaker_manual_reset", {"name": self.name}).inc()
    
    def force_open(self):
        """Manually force circuit breaker to open state."""
        old_state = self._state
        self._change_state(CircuitBreakerState.OPEN)
        self._next_attempt_time = datetime.now(timezone.utc) + timedelta(seconds=self.recovery_timeout)
        
        logger.warning(f"Circuit breaker '{self.name}' manually forced open from {old_state.value}")
        metrics.counter("circuit_breaker_manual_open", {"name": self.name}).inc()
    
    def is_healthy(self) -> bool:
        """Check if circuit breaker is in a healthy state."""
        return self._state == CircuitBreakerState.CLOSED and self.success_rate >= 95.0


def circuit_breaker(
    failure_threshold: int = 5,
    recovery_timeout: int = 60,
    expected_exception: type = Exception,
    name: Optional[str] = None,
    fallback: Optional[Callable] = None,
    success_threshold: int = 1,
    timeout: Optional[int] = None
):
    """
    Decorator for applying circuit breaker pattern to functions.
    
    Args:
        failure_threshold: Number of failures before opening circuit
        recovery_timeout: Seconds to wait before attempting recovery
        expected_exception: Exception type that counts as failure
        name: Name for the circuit breaker (defaults to function name)
        fallback: Fallback function to call when circuit is open
        success_threshold: Number of successes needed to close from half-open
        timeout: Request timeout in seconds
    """
    def decorator(func: Callable):
        breaker_name = name or f"{func.__module__}.{func.__name__}"
        breaker = CircuitBreaker(
            failure_threshold=failure_threshold,
            recovery_timeout=recovery_timeout,
            expected_exception=expected_exception,
            name=breaker_name,
            fallback_function=fallback,
            success_threshold=success_threshold,
            timeout=timeout
        )
        
        @wraps(func)
        async def async_wrapper(*args, **kwargs):
            return await breaker.call(func, *args, **kwargs)
        
        @wraps(func)
        def sync_wrapper(*args, **kwargs):
            return asyncio.run(breaker.call(func, *args, **kwargs))
        
        # Return appropriate wrapper based on function type
        if asyncio.iscoroutinefunction(func):
            wrapper = async_wrapper
        else:
            wrapper = sync_wrapper
        
        # Attach circuit breaker for inspection
        wrapper.circuit_breaker = breaker
        return wrapper
    
    return decorator


# Global circuit breaker registry for monitoring
_circuit_breakers: Dict[str, CircuitBreaker] = {}


def register_circuit_breaker(name: str, circuit_breaker: CircuitBreaker):
    """Register a circuit breaker for global monitoring."""
    _circuit_breakers[name] = circuit_breaker
    logger.debug(f"Registered circuit breaker: {name}")


def get_circuit_breaker_stats() -> Dict[str, Dict[str, Any]]:
    """Get statistics for all registered circuit breakers."""
    return {name: breaker.get_stats() for name, breaker in _circuit_breakers.items()}


def reset_all_circuit_breakers():
    """Reset all registered circuit breakers."""
    for name, breaker in _circuit_breakers.items():
        breaker.reset()
        logger.info(f"Reset circuit breaker: {name}")


def get_circuit_breaker_by_name(name: str) -> Optional[CircuitBreaker]:
    """Get a specific circuit breaker by name."""
    return _circuit_breakers.get(name)


def get_unhealthy_circuit_breakers() -> Dict[str, CircuitBreaker]:
    """Get all circuit breakers that are not in a healthy state."""
    return {
        name: breaker 
        for name, breaker in _circuit_breakers.items() 
        if not breaker.is_healthy()
    }


def get_open_circuit_breakers() -> Dict[str, CircuitBreaker]:
    """Get all circuit breakers that are currently open."""
    return {
        name: breaker 
        for name, breaker in _circuit_breakers.items() 
        if breaker.state == CircuitBreakerState.OPEN.value
    }


def get_half_open_circuit_breakers() -> Dict[str, CircuitBreaker]:
    """Get all circuit breakers that are currently half-open."""
    return {
        name: breaker 
        for name, breaker in _circuit_breakers.items() 
        if breaker.state == CircuitBreakerState.HALF_OPEN.value
    }


def get_circuit_breaker_summary() -> Dict[str, Any]:
    """Get a summary of all circuit breaker states."""
    total = len(_circuit_breakers)
    closed = len([cb for cb in _circuit_breakers.values() if cb.state == CircuitBreakerState.CLOSED.value])
    open_count = len([cb for cb in _circuit_breakers.values() if cb.state == CircuitBreakerState.OPEN.value])
    half_open = len([cb for cb in _circuit_breakers.values() if cb.state == CircuitBreakerState.HALF_OPEN.value])
    healthy = len([cb for cb in _circuit_breakers.values() if cb.is_healthy()])
    
    return {
        "total_circuit_breakers": total,
        "closed": closed,
        "open": open_count,
        "half_open": half_open,
        "healthy": healthy,
        "unhealthy": total - healthy,
        "overall_health_percentage": round((healthy / total * 100) if total > 0 else 100, 2)
    }


def force_open_circuit_breaker(name: str) -> bool:
    """Force a specific circuit breaker to open state."""
    breaker = _circuit_breakers.get(name)
    if breaker:
        breaker.force_open()
        logger.warning(f"Manually forced circuit breaker '{name}' to open state")
        return True
    return False


def reset_circuit_breaker(name: str) -> bool:
    """Reset a specific circuit breaker to closed state."""
    breaker = _circuit_breakers.get(name)
    if breaker:
        breaker.reset()
        logger.info(f"Manually reset circuit breaker '{name}' to closed state")
        return True
    return False


async def health_check_all_circuit_breakers() -> Dict[str, Dict[str, Any]]:
    """Perform health check on all circuit breakers."""
    health_status = {}
    
    for name, breaker in _circuit_breakers.items():
        stats = breaker.get_stats()
        health_status[name] = {
            "healthy": breaker.is_healthy(),
            "state": stats["state"],
            "success_rate": stats["success_rate"],
            "total_requests": stats["total_requests"],
            "failure_count": stats["failure_count"],
            "blocked_requests": stats["blocked_requests"],
            "has_fallback": stats["has_fallback"],
            "fallback_success_rate": stats["fallback_success_rate"],
            "average_response_time": stats["average_response_time"]
        }
    
    return health_status


class CircuitBreakerMiddleware:
    """Middleware to collect circuit breaker metrics and handle global fallbacks."""
    
    def __init__(self):
        self.global_fallbacks: Dict[str, Callable] = {}
    
    def register_global_fallback(self, service_name: str, fallback_func: Callable):
        """Register a global fallback function for a service."""
        self.global_fallbacks[service_name] = fallback_func
        logger.info(f"Registered global fallback for service: {service_name}")
    
    async def execute_with_global_fallback(
        self, 
        service_name: str, 
        func: Callable, 
        *args, 
        **kwargs
    ) -> Any:
        """Execute function with global fallback support."""
        try:
            if asyncio.iscoroutinefunction(func):
                return await func(*args, **kwargs)
            else:
                return func(*args, **kwargs)
        except CircuitBreakerError as e:
            # Try global fallback if available
            if service_name in self.global_fallbacks:
                logger.info(f"Using global fallback for service: {service_name}")
                fallback_func = self.global_fallbacks[service_name]
                
                if asyncio.iscoroutinefunction(fallback_func):
                    return await fallback_func(*args, **kwargs)
                else:
                    return fallback_func(*args, **kwargs)
            
            # No global fallback available
            raise


# Global circuit breaker middleware instance
circuit_breaker_middleware = CircuitBreakerMiddleware()


# Utility functions for common fallback patterns
async def empty_list_fallback(*args, **kwargs) -> list:
    """Fallback that returns an empty list."""
    logger.info("Using empty list fallback")
    return []


async def empty_dict_fallback(*args, **kwargs) -> dict:
    """Fallback that returns an empty dictionary."""
    logger.info("Using empty dict fallback")
    return {}


async def none_fallback(*args, **kwargs) -> None:
    """Fallback that returns None."""
    logger.info("Using None fallback")
    return None


async def cached_data_fallback(cache_key: str, cache_repo=None, *args, **kwargs) -> Any:
    """Fallback that tries to return cached data."""
    if cache_repo:
        try:
            cached_data = await cache_repo.get(cache_key)
            if cached_data:
                logger.info(f"Using cached data fallback for key: {cache_key}")
                return cached_data
        except Exception as e:
            logger.error(f"Failed to get cached data for fallback: {e}")
    
    logger.warning(f"No cached data available for fallback key: {cache_key}")
    return None


async def stale_data_fallback(cache_key: str, cache_repo=None, max_age_hours: int = 24, *args, **kwargs) -> Any:
    """Fallback that returns stale cached data if available."""
    if cache_repo:
        try:
            # Try to get stale data with extended TTL
            stale_key = f"stale:{cache_key}"
            stale_data = await cache_repo.get(stale_key)
            if stale_data:
                logger.info(f"Using stale data fallback for key: {cache_key}")
                return stale_data
        except Exception as e:
            logger.error(f"Failed to get stale data for fallback: {e}")
    
    logger.warning(f"No stale data available for fallback key: {cache_key}")
    return None


async def default_value_fallback(default_value: Any, *args, **kwargs) -> Any:
    """Fallback that returns a predefined default value."""
    logger.info(f"Using default value fallback: {default_value}")
    return default_value


async def partial_data_fallback(partial_data: Any, *args, **kwargs) -> Any:
    """Fallback that returns partial data when full data is unavailable."""
    logger.info("Using partial data fallback")
    return partial_data


class FallbackChain:
    """Chain multiple fallback functions together."""
    
    def __init__(self, *fallback_functions):
        self.fallback_functions = fallback_functions
    
    async def __call__(self, *args, **kwargs):
        """Execute fallback functions in order until one succeeds."""
        last_error = None
        
        for i, fallback_func in enumerate(self.fallback_functions):
            try:
                logger.debug(f"Trying fallback function {i + 1}/{len(self.fallback_functions)}")
                result = await fallback_func(*args, **kwargs)
                if result is not None:
                    logger.info(f"Fallback chain succeeded with function {i + 1}")
                    return result
            except Exception as e:
                logger.warning(f"Fallback function {i + 1} failed: {e}")
                last_error = e
                continue
        
        # All fallbacks failed
        logger.error("All fallback functions in chain failed")
        if last_error:
            raise last_error
        return None


def create_service_fallback(service_name: str, fallback_data: Any = None):
    """Create a service-specific fallback function."""
    async def service_fallback(*args, **kwargs):
        logger.warning(f"Service '{service_name}' is unavailable, using fallback")
        metrics.counter("service_fallback_used", {"service": service_name}).inc()
        
        if fallback_data is not None:
            return fallback_data
        
        # Return appropriate empty data based on service
        if service_name.lower() in ["algolia", "search", "product"]:
            return {} if args and isinstance(args[0], list) else None
        elif service_name.lower() in ["cache", "redis"]:
            return None
        elif service_name.lower() in ["cloudfront", "cdn"]:
            return {"status": "fallback", "message": "CDN service unavailable"}
        else:
            return None
    
    return service_fallback