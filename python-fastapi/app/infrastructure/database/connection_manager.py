"""
Database connection manager with pooling, monitoring, and graceful degradation.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from contextlib import asynccontextmanager
from dataclasses import dataclass, field

from app.infrastructure.database.dynamodb import DynamoD<PERSON>lient, DynamoDBOperations
from app.infrastructure.database.redis import RedisClient, RedisOperations
from app.config.settings import get_settings
from app.core.exceptions import ConnectionError, DatabaseError

logger = logging.getLogger(__name__)


@dataclass
class ConnectionStats:
    """Connection statistics for monitoring."""
    service_name: str
    is_healthy: bool
    last_health_check: datetime
    connection_count: int
    max_connections: int
    error_count: int = 0
    last_error: Optional[str] = None
    uptime_start: datetime = field(default_factory=datetime.utcnow)
    
    @property
    def uptime_seconds(self) -> float:
        """Get uptime in seconds."""
        return (datetime.utcnow() - self.uptime_start).total_seconds()
    
    @property
    def connection_utilization(self) -> float:
        """Get connection pool utilization percentage."""
        if self.max_connections == 0:
            return 0.0
        return (self.connection_count / self.max_connections) * 100


@dataclass
class HealthCheckResult:
    """Health check result for a service."""
    service_name: str
    is_healthy: bool
    response_time_ms: float
    error_message: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.utcnow)


class ConnectionManager:
    """Manages database connections with monitoring and graceful degradation."""
    
    def __init__(self, settings=None):
        self.settings = settings or get_settings()
        self._dynamodb_client: Optional[DynamoDBClient] = None
        self._redis_client: Optional[RedisClient] = None
        self._dynamodb_operations: Optional[DynamoDBOperations] = None
        self._redis_operations: Optional[RedisOperations] = None
        
        # Connection monitoring
        self._stats: Dict[str, ConnectionStats] = {}
        self._health_check_interval = 30  # seconds
        self._health_check_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()
        
        # Circuit breaker settings
        self._circuit_breaker_threshold = 5  # failures before opening circuit
        self._circuit_breaker_timeout = 60  # seconds to wait before retry
        self._circuit_breakers: Dict[str, Dict[str, Any]] = {}
        
        # Graceful degradation settings
        self._enable_graceful_degradation = True
        self._degradation_mode = False
    
    async def initialize(self):
        """Initialize all database connections."""
        logger.info("Initializing database connections...")
        
        try:
            # Initialize DynamoDB
            await self._initialize_dynamodb()
            
            # Initialize Redis
            await self._initialize_redis()
            
            # Start health monitoring
            await self._start_health_monitoring()
            
            logger.info("Database connections initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize database connections: {e}")
            if not self._enable_graceful_degradation:
                raise ConnectionError(f"Database initialization failed: {e}")
            else:
                logger.warning("Continuing with graceful degradation enabled")
                self._degradation_mode = True
    
    async def _initialize_dynamodb(self):
        """Initialize DynamoDB client and operations."""
        try:
            self._dynamodb_client = DynamoDBClient(self.settings)
            await self._dynamodb_client.initialize()
            self._dynamodb_operations = DynamoDBOperations(self._dynamodb_client)
            
            # Initialize stats
            stats = await self._dynamodb_client.get_connection_stats()
            self._stats['dynamodb'] = ConnectionStats(
                service_name='DynamoDB',
                is_healthy=self._dynamodb_client.is_healthy,
                last_health_check=datetime.utcnow(),
                connection_count=0,  # DynamoDB doesn't expose this directly
                max_connections=stats.get('pool_size', 100)
            )
            
            # Initialize circuit breaker
            self._circuit_breakers['dynamodb'] = {
                'state': 'closed',  # closed, open, half-open
                'failure_count': 0,
                'last_failure_time': None,
                'next_attempt_time': None
            }
            
            logger.info("DynamoDB client initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize DynamoDB: {e}")
            if self._enable_graceful_degradation:
                self._stats['dynamodb'] = ConnectionStats(
                    service_name='DynamoDB',
                    is_healthy=False,
                    last_health_check=datetime.utcnow(),
                    connection_count=0,
                    max_connections=0,
                    error_count=1,
                    last_error=str(e)
                )
            else:
                raise
    
    async def _initialize_redis(self):
        """Initialize Redis client and operations."""
        try:
            self._redis_client = RedisClient(self.settings)
            await self._redis_client.initialize()
            self._redis_operations = RedisOperations(self._redis_client)
            
            # Initialize stats
            stats = await self._redis_client.get_connection_stats()
            self._stats['redis'] = ConnectionStats(
                service_name='Redis',
                is_healthy=self._redis_client.is_healthy,
                last_health_check=datetime.utcnow(),
                connection_count=stats.get('in_use_connections', 0),
                max_connections=stats.get('max_connections', 100)
            )
            
            # Initialize circuit breaker
            self._circuit_breakers['redis'] = {
                'state': 'closed',
                'failure_count': 0,
                'last_failure_time': None,
                'next_attempt_time': None
            }
            
            logger.info("Redis client initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis: {e}")
            if self._enable_graceful_degradation:
                self._stats['redis'] = ConnectionStats(
                    service_name='Redis',
                    is_healthy=False,
                    last_health_check=datetime.utcnow(),
                    connection_count=0,
                    max_connections=0,
                    error_count=1,
                    last_error=str(e)
                )
            else:
                raise
    
    async def _start_health_monitoring(self):
        """Start background health monitoring task."""
        if self._health_check_task and not self._health_check_task.done():
            return
        
        self._health_check_task = asyncio.create_task(self._health_monitor_loop())
        logger.info("Health monitoring started")
    
    async def _health_monitor_loop(self):
        """Background health monitoring loop."""
        while not self._shutdown_event.is_set():
            try:
                await self._perform_health_checks()
                await asyncio.sleep(self._health_check_interval)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in health monitoring loop: {e}")
                await asyncio.sleep(5)  # Short delay before retry
    
    async def _perform_health_checks(self):
        """Perform health checks on all connections."""
        health_results = []
        
        # Check DynamoDB
        if self._dynamodb_client:
            result = await self._check_service_health('dynamodb', self._dynamodb_client.health_check)
            health_results.append(result)
        
        # Check Redis
        if self._redis_client:
            result = await self._check_service_health('redis', self._redis_client.health_check)
            health_results.append(result)
        
        # Update circuit breakers
        for result in health_results:
            await self._update_circuit_breaker(result.service_name.lower(), result.is_healthy)
        
        # Log health status
        healthy_services = [r.service_name for r in health_results if r.is_healthy]
        unhealthy_services = [r.service_name for r in health_results if not r.is_healthy]
        
        if unhealthy_services:
            logger.warning(f"Unhealthy services: {unhealthy_services}")
        
        logger.debug(f"Health check completed. Healthy: {healthy_services}")
    
    async def _check_service_health(self, service_name: str, health_check_func) -> HealthCheckResult:
        """Check health of a specific service."""
        start_time = datetime.utcnow()
        
        try:
            is_healthy = await asyncio.wait_for(health_check_func(), timeout=10.0)
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Update stats
            if service_name in self._stats:
                self._stats[service_name].is_healthy = is_healthy
                self._stats[service_name].last_health_check = datetime.utcnow()
                
                if not is_healthy:
                    self._stats[service_name].error_count += 1
            
            return HealthCheckResult(
                service_name=service_name.title(),
                is_healthy=is_healthy,
                response_time_ms=response_time
            )
            
        except Exception as e:
            response_time = (datetime.utcnow() - start_time).total_seconds() * 1000
            
            # Update stats
            if service_name in self._stats:
                self._stats[service_name].is_healthy = False
                self._stats[service_name].last_health_check = datetime.utcnow()
                self._stats[service_name].error_count += 1
                self._stats[service_name].last_error = str(e)
            
            return HealthCheckResult(
                service_name=service_name.title(),
                is_healthy=False,
                response_time_ms=response_time,
                error_message=str(e)
            )
    
    async def _update_circuit_breaker(self, service_name: str, is_healthy: bool):
        """Update circuit breaker state based on health check."""
        if service_name not in self._circuit_breakers:
            return
        
        breaker = self._circuit_breakers[service_name]
        now = datetime.utcnow()
        
        if is_healthy:
            # Reset on success
            breaker['failure_count'] = 0
            breaker['last_failure_time'] = None
            
            # Close circuit if it was open
            if breaker['state'] == 'half-open':
                breaker['state'] = 'closed'
                logger.info(f"Circuit breaker for {service_name} closed (recovered)")
                
        else:
            # Increment failure count
            breaker['failure_count'] += 1
            breaker['last_failure_time'] = now
            
            # Open circuit if threshold exceeded
            if (breaker['state'] == 'closed' and 
                breaker['failure_count'] >= self._circuit_breaker_threshold):
                
                breaker['state'] = 'open'
                breaker['next_attempt_time'] = now + timedelta(seconds=self._circuit_breaker_timeout)
                logger.warning(f"Circuit breaker for {service_name} opened (failures: {breaker['failure_count']})")
            
            # Try half-open if timeout passed
            elif (breaker['state'] == 'open' and 
                  breaker['next_attempt_time'] and 
                  now >= breaker['next_attempt_time']):
                
                breaker['state'] = 'half-open'
                logger.info(f"Circuit breaker for {service_name} half-open (attempting recovery)")
    
    def _is_circuit_open(self, service_name: str) -> bool:
        """Check if circuit breaker is open for a service."""
        if service_name not in self._circuit_breakers:
            return False
        
        return self._circuit_breakers[service_name]['state'] == 'open'
    
    @asynccontextmanager
    async def get_dynamodb_operations(self):
        """Get DynamoDB operations with circuit breaker protection."""
        if self._is_circuit_open('dynamodb'):
            raise DatabaseError("DynamoDB circuit breaker is open")
        
        if not self._dynamodb_operations:
            if self._enable_graceful_degradation:
                raise DatabaseError("DynamoDB not available (graceful degradation)")
            else:
                raise ConnectionError("DynamoDB not initialized")
        
        try:
            yield self._dynamodb_operations
        except Exception as e:
            # Update circuit breaker on error
            await self._update_circuit_breaker('dynamodb', False)
            raise
    
    @asynccontextmanager
    async def get_redis_operations(self):
        """Get Redis operations with circuit breaker protection."""
        if self._is_circuit_open('redis'):
            if self._enable_graceful_degradation:
                # Return a no-op Redis operations for graceful degradation
                yield NoOpRedisOperations()
                return
            else:
                raise DatabaseError("Redis circuit breaker is open")
        
        if not self._redis_operations:
            if self._enable_graceful_degradation:
                yield NoOpRedisOperations()
                return
            else:
                raise ConnectionError("Redis not initialized")
        
        try:
            yield self._redis_operations
        except Exception as e:
            # Update circuit breaker on error
            await self._update_circuit_breaker('redis', False)
            
            if self._enable_graceful_degradation:
                logger.warning(f"Redis operation failed, using no-op: {e}")
                yield NoOpRedisOperations()
            else:
                raise
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get comprehensive connection statistics."""
        stats = {}
        
        for service_name, service_stats in self._stats.items():
            circuit_breaker = self._circuit_breakers.get(service_name, {})
            
            stats[service_name] = {
                'service_name': service_stats.service_name,
                'is_healthy': service_stats.is_healthy,
                'last_health_check': service_stats.last_health_check.isoformat(),
                'connection_count': service_stats.connection_count,
                'max_connections': service_stats.max_connections,
                'connection_utilization': service_stats.connection_utilization,
                'error_count': service_stats.error_count,
                'last_error': service_stats.last_error,
                'uptime_seconds': service_stats.uptime_seconds,
                'circuit_breaker_state': circuit_breaker.get('state', 'unknown'),
                'circuit_breaker_failures': circuit_breaker.get('failure_count', 0)
            }
        
        stats['overall'] = {
            'degradation_mode': self._degradation_mode,
            'graceful_degradation_enabled': self._enable_graceful_degradation,
            'total_services': len(self._stats),
            'healthy_services': sum(1 for s in self._stats.values() if s.is_healthy),
            'unhealthy_services': sum(1 for s in self._stats.values() if not s.is_healthy)
        }
        
        return stats
    
    async def shutdown(self):
        """Gracefully shutdown all connections."""
        logger.info("Shutting down database connections...")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Cancel health monitoring
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # Close connections
        if self._redis_client:
            await self._redis_client.close()
        
        # DynamoDB client doesn't need explicit closing
        
        logger.info("Database connections shutdown completed")


class NoOpRedisOperations:
    """No-op Redis operations for graceful degradation."""
    
    async def get(self, key: str) -> None:
        return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        return True  # Pretend success
    
    async def delete(self, key: str) -> bool:
        return True
    
    async def delete_pattern(self, pattern: str) -> int:
        return 0
    
    async def exists(self, key: str) -> bool:
        return False
    
    async def expire(self, key: str, ttl: int) -> bool:
        return True
    
    async def mget(self, keys: List[str]) -> List[None]:
        return [None] * len(keys)
    
    async def mset(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        return True
    
    async def health_check(self) -> bool:
        return False


# Global connection manager instance
_connection_manager: Optional[ConnectionManager] = None


async def get_connection_manager() -> ConnectionManager:
    """Get the global connection manager instance."""
    global _connection_manager
    
    if _connection_manager is None:
        _connection_manager = ConnectionManager()
        await _connection_manager.initialize()
    
    return _connection_manager


async def init_connection_manager(settings=None) -> ConnectionManager:
    """Initialize connection manager during application startup."""
    global _connection_manager
    
    _connection_manager = ConnectionManager(settings)
    await _connection_manager.initialize()
    
    return _connection_manager


async def shutdown_connection_manager():
    """Shutdown connection manager during application shutdown."""
    global _connection_manager
    
    if _connection_manager:
        await _connection_manager.shutdown()
        _connection_manager = None