"""
Wishlist management endpoints.

This module contains all the REST API endpoints for wishlist operations
including CRUD operations, privacy settings, and sharing functionality.
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from fastapi.responses import JSONResponse

from app.services.wishlist_service import WishlistService
from app.schemas.requests.wishlist import (
    CreateWishlistRequest,
    UpdateWishlistRequest,
    UpdateWishlistPrivacyRequest
)
from app.schemas.responses.wishlist import (
    WishlistResponse,
    WishlistListResponse
)
from app.schemas.responses.common import (
    SuccessResponseSchema,
    ErrorResponseSchema
)
from app.api.dependencies import (
    get_wishlist_service,
    get_current_user_id,
    validate_country_language
)
from app.core.exceptions import WishlistNotFoundError, ValidationError
from app.utils.helpers import generate_correlation_id
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/",
    response_model=WishlistListResponse,
    summary="Get user wishlists",
    description="Retrieve all wishlists for the authenticated user with optional product enrichment"
)
async def get_user_wishlists(
    country: str = Query("ae", description="Country code for product localization"),
    language: str = Query("en", description="Language code for product localization"),
    include_products: bool = Query(True, description="Whether to include product details"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service),
    _: dict = Depends(validate_country_language)
) -> WishlistListResponse:
    """Get all wishlists for the authenticated user."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Getting wishlists for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "country": country,
            "language": language
        })

        wishlists = await wishlist_service.get_user_wishlists(
            user_id=user_id,
            country=country,
            language=language,
            include_products=include_products
        )

        # Convert domain models to response schemas
        wishlist_responses = [
            WishlistResponse.from_domain_model(wishlist)
            for wishlist in wishlists
        ]

        return WishlistListResponse(
            wishlists=wishlist_responses,
            total=len(wishlist_responses)
        )

    except Exception as e:
        logger.error(f"Failed to get user wishlists: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to retrieve wishlists",
                correlation_id=correlation_id
            ).dict()
        )


@router.post(
    "/",
    response_model=WishlistResponse,
    status_code=201,
    summary="Create wishlist",
    description="Create a new wishlist for the authenticated user"
)
async def create_wishlist(
    request: CreateWishlistRequest,
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Create a new wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Creating wishlist for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_name": request.name
        })

        wishlist = await wishlist_service.create_wishlist(user_id, request)

        return WishlistResponse.from_domain_model(wishlist)

    except ValidationError as e:
        logger.warning(f"Validation error creating wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to create wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to create wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.get(
    "/{wishlist_id}",
    response_model=WishlistResponse,
    summary="Get wishlist by ID",
    description="Retrieve a specific wishlist by its ID"
)
async def get_wishlist(
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    country: str = Query("ae", description="Country code for product localization"),
    language: str = Query("en", description="Language code for product localization"),
    include_products: bool = Query(True, description="Whether to include product details"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service),
    _: dict = Depends(validate_country_language)
) -> WishlistResponse:
    """Get a specific wishlist by ID."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Getting wishlist {wishlist_id} for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        wishlist = await wishlist_service.get_wishlist_by_id(
            user_id=user_id,
            wishlist_id=wishlist_id,
            country=country,
            language=language,
            include_products=include_products
        )

        if not wishlist:
            raise HTTPException(
                status_code=404,
                detail=ErrorResponseSchema(
                    code="WISHLIST_NOT_FOUND",
                    message=f"Wishlist {wishlist_id} not found",
                    correlation_id=correlation_id
                ).dict()
            )

        return WishlistResponse.from_domain_model(wishlist)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to retrieve wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.put(
    "/{wishlist_id}",
    response_model=WishlistResponse,
    summary="Update wishlist",
    description="Update a wishlist's properties"
)
async def update_wishlist(
    request: UpdateWishlistRequest,
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> WishlistResponse:
    """Update a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Updating wishlist {wishlist_id} for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        wishlist = await wishlist_service.update_wishlist(user_id, wishlist_id, request)

        return WishlistResponse.from_domain_model(wishlist)

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except ValidationError as e:
        logger.warning(f"Validation error updating wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=400,
            detail=ErrorResponseSchema(
                code="VALIDATION_ERROR",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to update wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to update wishlist",
                correlation_id=correlation_id
            ).dict()
        )


@router.delete(
    "/{wishlist_id}",
    response_model=SuccessResponseSchema,
    summary="Delete wishlist",
    description="Delete a wishlist and all its items"
)
async def delete_wishlist(
    wishlist_id: str = Path(..., description="The ID of the wishlist"),
    user_id: str = Depends(get_current_user_id),
    wishlist_service: WishlistService = Depends(get_wishlist_service)
) -> SuccessResponseSchema:
    """Delete a wishlist."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Deleting wishlist {wishlist_id} for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        success = await wishlist_service.delete_wishlist(user_id, wishlist_id)

        if success:
            return SuccessResponseSchema(
                message="Wishlist deleted successfully",
                correlation_id=correlation_id
            )
        else:
            raise HTTPException(
                status_code=500,
                detail=ErrorResponseSchema(
                    code="DELETE_FAILED",
                    message="Failed to delete wishlist",
                    correlation_id=correlation_id
                ).dict()
            )

    except WishlistNotFoundError as e:
        logger.warning(f"Wishlist not found: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })
        raise HTTPException(
            status_code=404,
            detail=ErrorResponseSchema(
                code="WISHLIST_NOT_FOUND",
                message=str(e),
                correlation_id=correlation_id
            ).dict()
        )
    except Exception as e:
        logger.error(f"Failed to delete wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to delete wishlist",
                correlation_id=correlation_id
            ).dict()
        )
