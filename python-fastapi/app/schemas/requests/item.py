"""
Wishlist item request schemas.

This module contains Pydantic schemas for validating wishlist item-related API requests.
"""

from pydantic import Field, field_validator, model_validator
from typing import Optional, List, Dict, Any
from datetime import datetime

from ..base import (
    BaseRequestSchema,
    validate_user_id,
    validate_product_id,
    validate_notes,
    CountryCode,
    LanguageCode
)


class AddItemRequest(BaseRequestSchema):
    """
    Schema for adding an item to a wishlist.
    
    Validates product ID and optional notes with comprehensive business rules.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user adding the item",
        example="user_12345"
    )
    product_id: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="ID of the product to add to the wishlist",
        example="product_abc123"
    )
    notes: Optional[str] = Field(
        default=None,
        max_length=500,
        description="Optional notes about the item",
        example="Size M, Blue color preferred"
    )
    quantity: Optional[int] = Field(
        default=1,
        ge=1,
        le=99,
        description="Desired quantity (for reference only)"
    )
    priority: Optional[int] = Field(
        default=1,
        ge=1,
        le=5,
        description="Priority level (1=low, 5=high)"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    @field_validator('product_id')
    @classmethod
    def validate_product_id_field(cls, v):
        return validate_product_id(v)
    
    @field_validator('notes')
    @classmethod
    def validate_notes_field(cls, v):
        return validate_notes(v)
    
    @field_validator('quantity')
    @classmethod
    def validate_quantity(cls, v):
        """Validate quantity value."""
        if v is not None and (v < 1 or v > 99):
            raise ValueError("quantity must be between 1 and 99")
        return v or 1
    
    @field_validator('priority')
    @classmethod
    def validate_priority(cls, v):
        """Validate priority value."""
        if v is not None and (v < 1 or v > 5):
            raise ValueError("priority must be between 1 and 5")
        return v or 1
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "product_id": "product_abc123",
                    "notes": "Size M, Blue color preferred",
                    "quantity": 1,
                    "priority": 3,
                    "country": "ae",
                    "language": "en"
                },
                {
                    "user_id": "user_67890",
                    "product_id": "product_xyz789",
                    "notes": "هدية لصديقي",
                    "quantity": 2,
                    "priority": 5,
                    "country": "sa",
                    "language": "ar"
                }
            ]
        }


class UpdateItemRequest(BaseRequestSchema):
    """
    Schema for updating an existing item in a wishlist.
    
    All fields except user_id are optional to support partial updates.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user updating the item",
        example="user_12345"
    )
    notes: Optional[str] = Field(
        default=None,
        max_length=500,
        description="Updated notes about the item",
        example="Updated: Size L, Red color preferred"
    )
    quantity: Optional[int] = Field(
        default=None,
        ge=1,
        le=99,
        description="Updated desired quantity"
    )
    priority: Optional[int] = Field(
        default=None,
        ge=1,
        le=5,
        description="Updated priority level"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    @field_validator('notes')
    @classmethod
    def validate_notes_field(cls, v):
        return validate_notes(v)
    
    @field_validator('quantity')
    @classmethod
    def validate_quantity(cls, v):
        """Validate quantity value."""
        if v is not None and (v < 1 or v > 99):
            raise ValueError("quantity must be between 1 and 99")
        return v
    
    @field_validator('priority')
    @classmethod
    def validate_priority(cls, v):
        """Validate priority value."""
        if v is not None and (v < 1 or v > 5):
            raise ValueError("priority must be between 1 and 5")
        return v
    
    @model_validator(mode='after')
    def validate_update_request(self):
        """Ensure at least one field is being updated."""
        if all(v is None for v in [self.notes, self.quantity, self.priority]):
            raise ValueError("At least one field must be provided for update")
        return self
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "notes": "Updated: Size L, Red color preferred",
                    "quantity": 2,
                    "country": "ae",
                    "language": "en"
                },
                {
                    "user_id": "user_12345",
                    "priority": 5,
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class RemoveItemRequest(BaseRequestSchema):
    """
    Schema for removing an item from a wishlist.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user removing the item",
        example="user_12345"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class BulkItemOperation(BaseRequestSchema):
    """
    Base schema for bulk item operations.
    """
    
    user_id: str = Field(
        ...,
        description="ID of the user performing the operation",
        example="user_12345"
    )
    
    # Validation
    @field_validator('user_id')
    @classmethod
    def validate_user_id_field(cls, v):
        return validate_user_id(v)


class BulkAddItemData(BaseRequestSchema):
    """
    Schema for individual item data in bulk add operations.
    """
    
    product_id: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="ID of the product to add",
        example="product_abc123"
    )
    notes: Optional[str] = Field(
        default=None,
        max_length=500,
        description="Optional notes about the item"
    )
    quantity: Optional[int] = Field(
        default=1,
        ge=1,
        le=99,
        description="Desired quantity"
    )
    priority: Optional[int] = Field(
        default=1,
        ge=1,
        le=5,
        description="Priority level"
    )
    
    # Validation
    @field_validator('product_id')
    @classmethod
    def validate_product_id_field(cls, v):
        return validate_product_id(v)
    
    @field_validator('notes')
    @classmethod
    def validate_notes_field(cls, v):
        return validate_notes(v)


class BulkAddItemsRequest(BulkItemOperation):
    """
    Schema for adding multiple items to a wishlist in a single operation.
    """
    
    items: List[BulkAddItemData] = Field(
        ...,
        min_items=1,
        max_items=50,
        description="List of items to add to the wishlist"
    )
    skip_duplicates: bool = Field(
        default=True,
        description="Whether to skip items that already exist in the wishlist"
    )
    
    @field_validator('items')
    @classmethod
    def validate_items(cls, v):
        """Validate items list."""
        if not v:
            raise ValueError("items list cannot be empty")
        
        if len(v) > 50:
            raise ValueError("cannot add more than 50 items at once")
        
        # Check for duplicate product IDs within the request
        product_ids = [item.product_id for item in v]
        if len(product_ids) != len(set(product_ids)):
            raise ValueError("duplicate product_ids found in request")
        
        return v
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "items": [
                        {
                            "product_id": "product_abc123",
                            "notes": "Size M",
                            "quantity": 1,
                            "priority": 3
                        },
                        {
                            "product_id": "product_xyz789",
                            "notes": "Blue color",
                            "quantity": 2,
                            "priority": 1
                        }
                    ],
                    "skip_duplicates": True,
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class BulkRemoveItemsRequest(BulkItemOperation):
    """
    Schema for removing multiple items from a wishlist in a single operation.
    """
    
    product_ids: List[str] = Field(
        ...,
        min_items=1,
        max_items=50,
        description="List of product IDs to remove from the wishlist"
    )
    ignore_missing: bool = Field(
        default=True,
        description="Whether to ignore product IDs that don't exist in the wishlist"
    )
    
    @field_validator('product_ids')
    @classmethod
    def validate_product_ids(cls, v):
        """Validate product IDs list."""
        if not v:
            raise ValueError("product_ids list cannot be empty")
        
        if len(v) > 50:
            raise ValueError("cannot remove more than 50 items at once")
        
        # Validate each product ID
        validated_ids = []
        for product_id in v:
            validated_id = validate_product_id(product_id)
            validated_ids.append(validated_id)
        
        # Check for duplicates
        if len(validated_ids) != len(set(validated_ids)):
            raise ValueError("duplicate product_ids found in request")
        
        return validated_ids
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "product_ids": [
                        "product_abc123",
                        "product_xyz789",
                        "product_def456"
                    ],
                    "ignore_missing": True,
                    "country": "ae",
                    "language": "en"
                }
            ]
        }


class MoveItemsRequest(BulkItemOperation):
    """
    Schema for moving items between wishlists.
    """
    
    source_wishlist_id: str = Field(
        ...,
        min_length=1,
        description="ID of the source wishlist",
        example="wishlist_source123"
    )
    target_wishlist_id: str = Field(
        ...,
        min_length=1,
        description="ID of the target wishlist",
        example="wishlist_target456"
    )
    product_ids: List[str] = Field(
        ...,
        min_items=1,
        max_items=50,
        description="List of product IDs to move"
    )
    copy_instead_of_move: bool = Field(
        default=False,
        description="Whether to copy items instead of moving them"
    )
    
    @field_validator('product_ids')
    @classmethod
    def validate_product_ids(cls, v):
        """Validate product IDs list."""
        if not v:
            raise ValueError("product_ids list cannot be empty")
        
        if len(v) > 50:
            raise ValueError("cannot move more than 50 items at once")
        
        # Validate each product ID and check for duplicates
        validated_ids = []
        for product_id in v:
            validated_id = validate_product_id(product_id)
            validated_ids.append(validated_id)
        
        if len(validated_ids) != len(set(validated_ids)):
            raise ValueError("duplicate product_ids found in request")
        
        return validated_ids
    
    @model_validator(mode='after')
    def validate_different_wishlists(self):
        """Ensure source and target wishlists are different."""
        if self.source_wishlist_id and self.target_wishlist_id and self.source_wishlist_id == self.target_wishlist_id:
            raise ValueError("source and target wishlists must be different")
        return self
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "source_wishlist_id": "wishlist_source123",
                    "target_wishlist_id": "wishlist_target456",
                    "product_ids": [
                        "product_abc123",
                        "product_xyz789"
                    ],
                    "copy_instead_of_move": False,
                    "country": "ae",
                    "language": "en"
                }
            ]
        }