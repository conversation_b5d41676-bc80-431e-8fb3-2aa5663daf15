"""
Wishlist response schemas.

This module contains Pydantic schemas for serializing wishlist-related API responses.
All schemas include proper field documentation and examples.
"""

from pydantic import Field, field_validator, model_validator
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..base import BaseResponseSchema, PaginatedResponseSchema, SuccessResponseSchema


class ProductDataResponse(BaseResponseSchema):
    """
    Schema for enriched product data from external services.
    
    This represents the product information fetched from Algolia or other sources.
    """
    
    product_id: str = Field(
        description="Product ID",
        example="product_abc123"
    )
    title: Optional[str] = Field(
        default=None,
        description="Product title",
        example="Wireless Bluetooth Headphones"
    )
    description: Optional[str] = Field(
        default=None,
        description="Product description"
    )
    price: Optional[float] = Field(
        default=None,
        description="Product price",
        example=99.99
    )
    currency: Optional[str] = Field(
        default=None,
        description="Price currency",
        example="AED"
    )
    image_url: Optional[str] = Field(
        default=None,
        description="Product image URL",
        example="https://example.com/images/product_abc123.jpg"
    )
    brand: Optional[str] = Field(
        default=None,
        description="Product brand",
        example="TechBrand"
    )
    category: Optional[str] = Field(
        default=None,
        description="Product category",
        example="Electronics > Audio"
    )
    availability: Optional[str] = Field(
        default=None,
        description="Product availability status",
        example="in_stock"
    )
    rating: Optional[float] = Field(
        default=None,
        description="Product rating",
        example=4.5
    )
    review_count: Optional[int] = Field(
        default=None,
        description="Number of reviews",
        example=128
    )
    url: Optional[str] = Field(
        default=None,
        description="Product page URL",
        example="https://example.com/products/product_abc123"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "product_id": "product_abc123",
                    "title": "Wireless Bluetooth Headphones",
                    "description": "High-quality wireless headphones with noise cancellation",
                    "price": 99.99,
                    "currency": "AED",
                    "image_url": "https://example.com/images/product_abc123.jpg",
                    "brand": "TechBrand",
                    "category": "Electronics > Audio",
                    "availability": "in_stock",
                    "rating": 4.5,
                    "review_count": 128,
                    "url": "https://example.com/products/product_abc123"
                }
            ]
        }


class WishlistItemResponse(BaseResponseSchema):
    """
    Schema for individual wishlist item responses.
    
    Includes both the item metadata and enriched product information.
    """
    
    product_id: str = Field(
        description="Product ID",
        example="product_abc123"
    )
    notes: Optional[str] = Field(
        default=None,
        description="User notes about the item",
        example="Size M, Blue color preferred"
    )
    quantity: Optional[int] = Field(
        default=1,
        description="Desired quantity",
        example=1
    )
    priority: Optional[int] = Field(
        default=1,
        description="Priority level (1=low, 5=high)",
        example=3
    )
    added_at: datetime = Field(
        description="When the item was added to the wishlist",
        example="2024-01-15T10:30:00Z"
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        description="When the item was last updated",
        example="2024-01-16T14:20:00Z"
    )
    product: Optional[ProductDataResponse] = Field(
        default=None,
        description="Enriched product information"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "product_id": "product_abc123",
                    "notes": "Size M, Blue color preferred",
                    "quantity": 1,
                    "priority": 3,
                    "added_at": "2024-01-15T10:30:00Z",
                    "updated_at": "2024-01-16T14:20:00Z",
                    "product": {
                        "product_id": "product_abc123",
                        "title": "Wireless Bluetooth Headphones",
                        "price": 99.99,
                        "currency": "AED",
                        "image_url": "https://example.com/images/product_abc123.jpg",
                        "brand": "TechBrand",
                        "availability": "in_stock"
                    }
                }
            ]
        }


class WishlistResponse(BaseResponseSchema):
    """
    Schema for complete wishlist responses.
    
    Includes all wishlist metadata and optionally the items list.
    """
    
    user_id: str = Field(
        description="ID of the user who owns the wishlist",
        example="user_12345"
    )
    wishlist_id: str = Field(
        description="Unique wishlist identifier",
        example="wishlist_abc123"
    )
    name: str = Field(
        description="Wishlist name",
        example="My Birthday Wishlist"
    )
    is_default: bool = Field(
        description="Whether this is the user's default wishlist",
        example=False
    )
    is_public: bool = Field(
        description="Whether the wishlist is publicly accessible",
        example=False
    )
    share_hash: str = Field(
        description="Hash for sharing the wishlist publicly",
        example="abc123def456ghi789"
    )
    share_url: Optional[str] = Field(
        default=None,
        description="Full URL for sharing the wishlist",
        example="https://api.example.com/api/v1/wishlists/shared/abc123def456ghi789"
    )
    item_count: int = Field(
        description="Total number of items in the wishlist",
        example=5
    )
    items: Optional[List[WishlistItemResponse]] = Field(
        default=None,
        description="List of items in the wishlist (if requested)"
    )
    created_at: datetime = Field(
        description="When the wishlist was created",
        example="2024-01-10T09:00:00Z"
    )
    updated_at: datetime = Field(
        description="When the wishlist was last updated",
        example="2024-01-16T14:20:00Z"
    )
    
    @model_validator(mode='after')
    def generate_share_url(self):
        """Generate share URL from share hash."""
        if self.share_url is None and self.share_hash:
            self.share_url = f"https://api.example.com/api/v1/wishlists/shared/{self.share_hash}"
        return self
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "user_id": "user_12345",
                    "wishlist_id": "wishlist_abc123",
                    "name": "My Birthday Wishlist",
                    "is_default": False,
                    "is_public": False,
                    "share_hash": "abc123def456ghi789",
                    "share_url": "https://api.example.com/api/v1/wishlists/shared/abc123def456ghi789",
                    "item_count": 2,
                    "items": [
                        {
                            "product_id": "product_abc123",
                            "notes": "Size M",
                            "quantity": 1,
                            "priority": 3,
                            "added_at": "2024-01-15T10:30:00Z",
                            "product": {
                                "product_id": "product_abc123",
                                "title": "Wireless Bluetooth Headphones",
                                "price": 99.99,
                                "currency": "AED"
                            }
                        }
                    ],
                    "created_at": "2024-01-10T09:00:00Z",
                    "updated_at": "2024-01-16T14:20:00Z"
                }
            ]
        }


class WishlistSummaryResponse(BaseResponseSchema):
    """
    Schema for wishlist summary responses (without items).
    
    Used for list views where full item details aren't needed.
    """
    
    user_id: str = Field(
        description="ID of the user who owns the wishlist"
    )
    wishlist_id: str = Field(
        description="Unique wishlist identifier"
    )
    name: str = Field(
        description="Wishlist name"
    )
    is_default: bool = Field(
        description="Whether this is the user's default wishlist"
    )
    is_public: bool = Field(
        description="Whether the wishlist is publicly accessible"
    )
    item_count: int = Field(
        description="Total number of items in the wishlist"
    )
    created_at: datetime = Field(
        description="When the wishlist was created"
    )
    updated_at: datetime = Field(
        description="When the wishlist was last updated"
    )


class WishlistListResponse(PaginatedResponseSchema):
    """
    Schema for paginated wishlist list responses.
    """
    
    wishlists: List[WishlistSummaryResponse] = Field(
        description="List of wishlists for the current page"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "wishlists": [
                        {
                            "user_id": "user_12345",
                            "wishlist_id": "wishlist_abc123",
                            "name": "My Birthday Wishlist",
                            "is_default": True,
                            "is_public": False,
                            "item_count": 5,
                            "created_at": "2024-01-10T09:00:00Z",
                            "updated_at": "2024-01-16T14:20:00Z"
                        },
                        {
                            "user_id": "user_12345",
                            "wishlist_id": "wishlist_def456",
                            "name": "Christmas Gifts",
                            "is_default": False,
                            "is_public": True,
                            "item_count": 3,
                            "created_at": "2024-01-05T15:30:00Z",
                            "updated_at": "2024-01-15T11:45:00Z"
                        }
                    ],
                    "total": 2,
                    "page": 1,
                    "page_size": 20,
                    "total_pages": 1,
                    "has_next": False,
                    "has_previous": False
                }
            ]
        }


class WishlistCreatedResponse(SuccessResponseSchema):
    """
    Schema for wishlist creation success responses.
    """
    
    wishlist: WishlistResponse = Field(
        description="The created wishlist"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Wishlist created successfully",
                    "wishlist": {
                        "user_id": "user_12345",
                        "wishlist_id": "wishlist_abc123",
                        "name": "My New Wishlist",
                        "is_default": False,
                        "is_public": False,
                        "share_hash": "abc123def456ghi789",
                        "item_count": 0,
                        "items": [],
                        "created_at": "2024-01-16T15:30:00Z",
                        "updated_at": "2024-01-16T15:30:00Z"
                    },
                    "request_id": "req_abc123",
                    "timestamp": "2024-01-16T15:30:00Z"
                }
            ]
        }


class WishlistUpdatedResponse(SuccessResponseSchema):
    """
    Schema for wishlist update success responses.
    """
    
    wishlist: WishlistResponse = Field(
        description="The updated wishlist"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Wishlist updated successfully",
                    "wishlist": {
                        "user_id": "user_12345",
                        "wishlist_id": "wishlist_abc123",
                        "name": "Updated Wishlist Name",
                        "is_default": True,
                        "is_public": False,
                        "share_hash": "abc123def456ghi789",
                        "item_count": 3,
                        "created_at": "2024-01-10T09:00:00Z",
                        "updated_at": "2024-01-16T15:45:00Z"
                    },
                    "request_id": "req_def456",
                    "timestamp": "2024-01-16T15:45:00Z"
                }
            ]
        }


class WishlistDeletedResponse(SuccessResponseSchema):
    """
    Schema for wishlist deletion success responses.
    """
    
    deleted_wishlist_id: str = Field(
        description="ID of the deleted wishlist",
        example="wishlist_abc123"
    )
    items_deleted: int = Field(
        description="Number of items that were deleted with the wishlist",
        example=5
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Wishlist deleted successfully",
                    "deleted_wishlist_id": "wishlist_abc123",
                    "items_deleted": 5,
                    "request_id": "req_ghi789",
                    "timestamp": "2024-01-16T16:00:00Z"
                }
            ]
        }


class ShareHashResponse(SuccessResponseSchema):
    """
    Schema for share hash regeneration responses.
    """
    
    share_hash: str = Field(
        description="The new share hash",
        example="new123hash456generated789"
    )
    share_url: str = Field(
        description="The new share URL",
        example="https://api.example.com/api/v1/wishlists/shared/new123hash456generated789"
    )
    
    class Config:
        schema_extra = {
            "examples": [
                {
                    "message": "Share hash regenerated successfully",
                    "share_hash": "new123hash456generated789",
                    "share_url": "https://api.example.com/api/v1/wishlists/shared/new123hash456generated789",
                    "request_id": "req_jkl012",
                    "timestamp": "2024-01-16T16:15:00Z"
                }
            ]
        }