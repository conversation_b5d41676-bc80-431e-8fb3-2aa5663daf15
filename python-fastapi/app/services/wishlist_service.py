"""
Core wishlist business logic service.

This service implements all wishlist operations including CRUD operations,
product enrichment, caching, and background job dispatching.
"""
import asyncio
import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import uuid4

from app.models.domain.wishlist import Wishlist, WishlistItem
from app.repositories.wishlist_repo import WishlistRepository
from app.repositories.cache_repo import CacheRepository
from app.services.algolia_service import AlgoliaService
from app.services.cloudfront_service import CloudFrontService
from app.schemas.requests.wishlist import (
    CreateWishlistRequest,
    UpdateWishlistRequest,
    AddItemRequest
)
from app.core.exceptions import (
    WishlistNotFoundError,
    ValidationError,
    ExternalServiceError
)
from app.core.metrics import metrics
from app.utils.helpers import generate_correlation_id

logger = logging.getLogger(__name__)


class WishlistService:
    """
    Core business logic service for wishlist operations.

    Handles all wishlist CRUD operations, product enrichment,
    caching strategies, and background job dispatching.
    """

    def __init__(
        self,
        wishlist_repo: WishlistRepository,
        cache_repo: CacheRepository,
        algolia_service: AlgoliaService,
        cloudfront_service: CloudFrontService,
    ):
        self.wishlist_repo = wishlist_repo
        self.cache_repo = cache_repo
        self.algolia_service = algolia_service
        self.cloudfront_service = cloudfront_service

    async def create_wishlist(
        self,
        user_id: str,
        request: CreateWishlistRequest
    ) -> Wishlist:
        """
        Create a new wishlist for a user.

        Args:
            user_id: The ID of the user creating the wishlist
            request: The wishlist creation request data

        Returns:
            The created wishlist

        Raises:
            ValidationError: If the request data is invalid
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Creating wishlist for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_name": request.name
        })

        with metrics.timer("wishlist_create_duration"):
            try:
                # Handle default wishlist logic
                if request.is_default:
                    await self._unset_default_wishlist(user_id)

                # Create new wishlist using domain model factory
                wishlist = Wishlist.create_new(
                    user_id=user_id,
                    name=request.name,
                    is_default=request.is_default,
                    is_public=False  # New wishlists are private by default
                )

                # Save to database
                created_wishlist = await self.wishlist_repo.create(wishlist)

                # Invalidate user's wishlist cache
                await self._invalidate_user_cache(user_id)

                # Dispatch background notification (fire and forget)
                asyncio.create_task(
                    self._dispatch_wishlist_created_notification(created_wishlist)
                )

                metrics.counter("wishlist_created").inc()
                logger.info(f"Successfully created wishlist {created_wishlist.wishlist_id}", extra={
                    "correlation_id": correlation_id,
                    "wishlist_id": created_wishlist.wishlist_id
                })

                return created_wishlist

            except Exception as e:
                metrics.counter("wishlist_create_error").inc()
                logger.error(f"Failed to create wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "error": str(e)
                })
                raise

    async def get_user_wishlists(
        self,
        user_id: str,
        country: str = "ae",
        language: str = "en",
        include_products: bool = True
    ) -> List[Wishlist]:
        """
        Get all wishlists for a user with optional product enrichment.

        Args:
            user_id: The ID of the user
            country: Country code for product localization
            language: Language code for product localization
            include_products: Whether to enrich items with product data

        Returns:
            List of user's wishlists
        """
        correlation_id = generate_correlation_id()
        cache_key = f"user_wishlists:{user_id}:{country}:{language}:{include_products}"

        logger.info(f"Getting wishlists for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "country": country,
            "language": language
        })

        # Try cache first
        try:
            cached_result = await self.cache_repo.get(cache_key)
            if cached_result:
                metrics.counter("cache_hit", {"type": "user_wishlists"}).inc()
                logger.debug(f"Cache hit for user wishlists", extra={
                    "correlation_id": correlation_id,
                    "cache_key": cache_key
                })
                return cached_result
        except Exception as e:
            logger.warning(f"Cache lookup failed: {e}", extra={
                "correlation_id": correlation_id
            })

        with metrics.timer("get_user_wishlists_duration"):
            try:
                # Get from database
                wishlists = await self.wishlist_repo.get_by_user_id(user_id)

                # Enrich with product details if requested
                if include_products and wishlists:
                    enriched_wishlists = await self._enrich_wishlists_with_products(
                        wishlists, country, language
                    )
                else:
                    enriched_wishlists = wishlists

                # Cache the result
                try:
                    await self.cache_repo.set(
                        cache_key,
                        enriched_wishlists,
                        ttl=300  # 5 minutes
                    )
                except Exception as e:
                    logger.warning(f"Failed to cache result: {e}", extra={
                        "correlation_id": correlation_id
                    })

                metrics.counter("cache_miss", {"type": "user_wishlists"}).inc()
                logger.info(f"Retrieved {len(enriched_wishlists)} wishlists for user", extra={
                    "correlation_id": correlation_id,
                    "wishlist_count": len(enriched_wishlists)
                })

                return enriched_wishlists

            except Exception as e:
                metrics.counter("get_user_wishlists_error").inc()
                logger.error(f"Failed to get user wishlists: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "error": str(e)
                })
                raise

    async def get_wishlist_by_id(
        self,
        user_id: str,
        wishlist_id: str,
        country: str = "ae",
        language: str = "en",
        include_products: bool = True
    ) -> Optional[Wishlist]:
        """
        Get a specific wishlist by ID.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            country: Country code for product localization
            language: Language code for product localization
            include_products: Whether to enrich items with product data

        Returns:
            The wishlist if found, None otherwise
        """
        correlation_id = generate_correlation_id()
        cache_key = f"wishlist:{user_id}:{wishlist_id}:{country}:{language}:{include_products}"

        logger.info(f"Getting wishlist {wishlist_id} for user {user_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        # Try cache first
        try:
            cached_result = await self.cache_repo.get(cache_key)
            if cached_result:
                metrics.counter("cache_hit", {"type": "wishlist"}).inc()
                return cached_result
        except Exception as e:
            logger.warning(f"Cache lookup failed: {e}", extra={
                "correlation_id": correlation_id
            })

        try:
            # Get from database
            wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
            if not wishlist:
                logger.info(f"Wishlist {wishlist_id} not found", extra={
                    "correlation_id": correlation_id
                })
                return None

            # Enrich with product details if requested
            if include_products:
                enriched_wishlist = await self._enrich_wishlist_with_products(
                    wishlist, country, language
                )
            else:
                enriched_wishlist = wishlist

            # Cache the result
            try:
                await self.cache_repo.set(cache_key, enriched_wishlist, ttl=300)
            except Exception as e:
                logger.warning(f"Failed to cache result: {e}", extra={
                    "correlation_id": correlation_id
                })

            metrics.counter("cache_miss", {"type": "wishlist"}).inc()
            return enriched_wishlist

        except Exception as e:
            metrics.counter("get_wishlist_error").inc()
            logger.error(f"Failed to get wishlist: {e}", extra={
                "correlation_id": correlation_id,
                "user_id": user_id,
                "wishlist_id": wishlist_id,
                "error": str(e)
            })
            raise

    async def update_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: UpdateWishlistRequest
    ) -> Wishlist:
        """
        Update a wishlist's properties.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist to update
            request: The update request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Updating wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        with metrics.timer("wishlist_update_duration"):
            try:
                # Get existing wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Handle default wishlist logic
                if request.is_default is not None and request.is_default and not wishlist.is_default:
                    await self._unset_default_wishlist(user_id)

                # Update fields
                if request.name is not None:
                    wishlist.name = request.name
                if request.is_default is not None:
                    wishlist.is_default = request.is_default

                wishlist.updated_at = datetime.utcnow()

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                metrics.counter("wishlist_updated").inc()
                logger.info(f"Successfully updated wishlist {wishlist_id}", extra={
                    "correlation_id": correlation_id
                })

                return updated_wishlist

            except WishlistNotFoundError:
                raise
            except Exception as e:
                metrics.counter("wishlist_update_error").inc()
                logger.error(f"Failed to update wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def delete_wishlist(self, user_id: str, wishlist_id: str) -> bool:
        """
        Delete a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist to delete

        Returns:
            True if deleted successfully

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Deleting wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id
        })

        with metrics.timer("wishlist_delete_duration"):
            try:
                # Verify wishlist exists
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Delete from database
                success = await self.wishlist_repo.delete(user_id, wishlist_id)

                if success:
                    # Invalidate cache
                    await self._invalidate_wishlist_cache(user_id, wishlist_id)

                    # Dispatch background notification
                    asyncio.create_task(
                        self._dispatch_wishlist_deleted_notification(wishlist)
                    )

                    metrics.counter("wishlist_deleted").inc()
                    logger.info(f"Successfully deleted wishlist {wishlist_id}", extra={
                        "correlation_id": correlation_id
                    })

                return success

            except WishlistNotFoundError:
                raise
            except Exception as e:
                metrics.counter("wishlist_delete_error").inc()
                logger.error(f"Failed to delete wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "error": str(e)
                })
                raise

    async def add_item_to_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        request: AddItemRequest
    ) -> Wishlist:
        """
        Add an item to a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            request: The add item request data

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Adding item {request.product_id} to wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": request.product_id
        })

        with metrics.timer("add_item_duration"):
            try:
                # Get wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Verify product exists in Algolia (optional validation)
                try:
                    product_exists = await self.algolia_service.product_exists(request.product_id)
                    if not product_exists:
                        logger.warning(f"Product {request.product_id} not found in Algolia", extra={
                            "correlation_id": correlation_id,
                            "product_id": request.product_id
                        })
                        # Continue anyway - product might be temporarily unavailable
                except Exception as e:
                    logger.warning(f"Failed to verify product existence: {e}", extra={
                        "correlation_id": correlation_id
                    })

                # Add item using domain model method
                added_item = wishlist.add_item(request.product_id, request.notes)

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                # Dispatch background notification
                asyncio.create_task(
                    self._dispatch_item_added_notification(updated_wishlist, added_item)
                )

                metrics.counter("item_added").inc()
                logger.info(f"Successfully added item to wishlist", extra={
                    "correlation_id": correlation_id,
                    "item_count": len(updated_wishlist.items)
                })

                return updated_wishlist

            except WishlistNotFoundError:
                raise
            except Exception as e:
                metrics.counter("add_item_error").inc()
                logger.error(f"Failed to add item to wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "product_id": request.product_id,
                    "error": str(e)
                })
                raise

    async def remove_item_from_wishlist(
        self,
        user_id: str,
        wishlist_id: str,
        product_id: str
    ) -> Wishlist:
        """
        Remove an item from a wishlist.

        Args:
            user_id: The ID of the user
            wishlist_id: The ID of the wishlist
            product_id: The ID of the product to remove

        Returns:
            The updated wishlist

        Raises:
            WishlistNotFoundError: If the wishlist doesn't exist
            ValidationError: If the item doesn't exist in the wishlist
        """
        correlation_id = generate_correlation_id()
        logger.info(f"Removing item {product_id} from wishlist {wishlist_id}", extra={
            "correlation_id": correlation_id,
            "user_id": user_id,
            "wishlist_id": wishlist_id,
            "product_id": product_id
        })

        with metrics.timer("remove_item_duration"):
            try:
                # Get wishlist
                wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
                if not wishlist:
                    raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")

                # Remove item using domain model method
                removed = wishlist.remove_item(product_id)
                if not removed:
                    raise ValidationError(f"Item {product_id} not found in wishlist")

                # Save to database
                updated_wishlist = await self.wishlist_repo.update(wishlist)

                # Invalidate cache
                await self._invalidate_wishlist_cache(user_id, wishlist_id)

                # Dispatch background notification
                asyncio.create_task(
                    self._dispatch_item_removed_notification(updated_wishlist, product_id)
                )

                metrics.counter("item_removed").inc()
                logger.info(f"Successfully removed item from wishlist", extra={
                    "correlation_id": correlation_id,
                    "item_count": len(updated_wishlist.items)
                })

                return updated_wishlist

            except (WishlistNotFoundError, ValidationError):
                raise
            except Exception as e:
                metrics.counter("remove_item_error").inc()
                logger.error(f"Failed to remove item from wishlist: {e}", extra={
                    "correlation_id": correlation_id,
                    "user_id": user_id,
                    "wishlist_id": wishlist_id,
                    "product_id": product_id,
                    "error": str(e)
                })
                raise

    async def get_shared_wishlist(
        self,
        share_hash: str,
        country: str = "ae",
        language: str = "en"
    ) -> Optional[Wishlist]:
        """
        Get a shared wishlist by its share hash.

        Args:
            share_hash: The share hash of the wishlist
            country: Country code for product localization
            language: Language code for product localization

        Returns:
            The shared wishlist if found and public, None otherwise
        """
        correlation_id = generate_correlation_id()
        cache_key = f"shared_wishlist:{share_hash}:{country}:{language}"

        logger.info(f"Getting shared wishlist {share_hash}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash
        })

        # Try cache first
        try:
            cached_result = await self.cache_repo.get(cache_key)
            if cached_result:
                metrics.counter("cache_hit", {"type": "shared_wishlist"}).inc()
                return cached_result
        except Exception as e:
            logger.warning(f"Cache lookup failed: {e}", extra={
                "correlation_id": correlation_id
            })

        try:
            # Get from database using share hash
            wishlist = await self.wishlist_repo.get_by_share_hash(share_hash)
            if not wishlist or not wishlist.is_public:
                logger.info(f"Shared wishlist not found or not public", extra={
                    "correlation_id": correlation_id,
                    "share_hash": share_hash
                })
                return None

            # Enrich with product details
            enriched_wishlist = await self._enrich_wishlist_with_products(
                wishlist, country, language
            )

            # Cache the result (longer TTL for shared wishlists)
            try:
                await self.cache_repo.set(cache_key, enriched_wishlist, ttl=1800)  # 30 minutes
            except Exception as e:
                logger.warning(f"Failed to cache result: {e}", extra={
                    "correlation_id": correlation_id
                })

            metrics.counter("cache_miss", {"type": "shared_wishlist"}).inc()
            return enriched_wishlist

        except Exception as e:
            metrics.counter("get_shared_wishlist_error").inc()
            logger.error(f"Failed to get shared wishlist: {e}", extra={
                "correlation_id": correlation_id,
                "share_hash": share_hash,
                "error": str(e)
            })
            raise

    # Helper methods

    async def _enrich_wishlists_with_products(
        self,
        wishlists: List[Wishlist],
        country: str,
        language: str
    ) -> List[Wishlist]:
        """Enrich multiple wishlists with product data concurrently."""
        if not wishlists:
            return wishlists

        tasks = [
            self._enrich_wishlist_with_products(wishlist, country, language)
            for wishlist in wishlists
        ]
        return await asyncio.gather(*tasks, return_exceptions=False)

    async def _enrich_wishlist_with_products(
        self,
        wishlist: Wishlist,
        country: str,
        language: str
    ) -> Wishlist:
        """Enrich a single wishlist with product data."""
        if not wishlist.items:
            return wishlist

        try:
            # Extract product IDs
            product_ids = [item.product_id for item in wishlist.items]

            # Get product data from Algolia
            products = await self.algolia_service.get_products_by_ids(
                product_ids, country, language
            )

            # Enrich items with product data
            for item in wishlist.items:
                if item.product_id in products:
                    item.product = products[item.product_id]
                else:
                    logger.warning(f"Product {item.product_id} not found in Algolia")

            return wishlist

        except Exception as e:
            logger.error(f"Failed to enrich wishlist with products: {e}")
            # Return wishlist without product enrichment rather than failing
            return wishlist

    async def _unset_default_wishlist(self, user_id: str) -> None:
        """Unset any existing default wishlist for a user."""
        try:
            current_default = await self.wishlist_repo.get_default_wishlist(user_id)
            if current_default:
                current_default.is_default = False
                current_default.updated_at = datetime.utcnow()
                await self.wishlist_repo.update(current_default)
                logger.info(f"Unset default wishlist {current_default.wishlist_id} for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to unset default wishlist: {e}")
            # Don't raise - this is not critical for the main operation

    async def _invalidate_user_cache(self, user_id: str) -> None:
        """Invalidate all cache entries for a user."""
        try:
            patterns = [
                f"user_wishlists:{user_id}:*",
                f"wishlist:{user_id}:*"
            ]
            for pattern in patterns:
                await self.cache_repo.delete_pattern(pattern)
            logger.debug(f"Invalidated cache for user {user_id}")
        except Exception as e:
            logger.warning(f"Failed to invalidate user cache: {e}")

    async def _invalidate_wishlist_cache(self, user_id: str, wishlist_id: str) -> None:
        """Invalidate cache entries for a specific wishlist."""
        try:
            patterns = [
                f"wishlist:{user_id}:{wishlist_id}:*",
                f"user_wishlists:{user_id}:*"
            ]
            for pattern in patterns:
                await self.cache_repo.delete_pattern(pattern)
            logger.debug(f"Invalidated cache for wishlist {wishlist_id}")
        except Exception as e:
            logger.warning(f"Failed to invalidate wishlist cache: {e}")

    # Background notification methods (fire and forget)

    async def _dispatch_wishlist_created_notification(self, wishlist: Wishlist) -> None:
        """Dispatch background notification for wishlist creation."""
        try:
            # This would typically queue a Celery task
            # For now, we'll just log the event
            logger.info(f"Wishlist created notification", extra={
                "event": "wishlist_created",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "wishlist_name": wishlist.name
            })

            # Trigger CloudFront cache invalidation if needed
            if wishlist.is_public:
                await self.cloudfront_service.invalidate_cache([
                    f"/api/v1/wishlists/shared/{wishlist.share_hash}"
                ])

        except Exception as e:
            logger.error(f"Failed to dispatch wishlist created notification: {e}")

    async def _dispatch_wishlist_deleted_notification(self, wishlist: Wishlist) -> None:
        """Dispatch background notification for wishlist deletion."""
        try:
            logger.info(f"Wishlist deleted notification", extra={
                "event": "wishlist_deleted",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id
            })

            # Trigger CloudFront cache invalidation
            if wishlist.is_public:
                await self.cloudfront_service.invalidate_cache([
                    f"/api/v1/wishlists/shared/{wishlist.share_hash}"
                ])

        except Exception as e:
            logger.error(f"Failed to dispatch wishlist deleted notification: {e}")

    async def _dispatch_item_added_notification(self, wishlist: Wishlist, item: WishlistItem) -> None:
        """Dispatch background notification for item addition."""
        try:
            logger.info(f"Item added notification", extra={
                "event": "item_added",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "product_id": item.product_id
            })
        except Exception as e:
            logger.error(f"Failed to dispatch item added notification: {e}")

    async def _dispatch_item_removed_notification(self, wishlist: Wishlist, product_id: str) -> None:
        """Dispatch background notification for item removal."""
        try:
            logger.info(f"Item removed notification", extra={
                "event": "item_removed",
                "user_id": wishlist.user_id,
                "wishlist_id": wishlist.wishlist_id,
                "product_id": product_id
            })
        except Exception as e:
            logger.error(f"Failed to dispatch item removed notification: {e}")
