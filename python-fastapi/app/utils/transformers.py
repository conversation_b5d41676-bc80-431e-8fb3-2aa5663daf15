"""
Data transformation utilities.

This module provides utilities for transforming data between different formats,
including domain models, database representations, API schemas, and external service formats.
"""

from typing import Dict, Any, List, Optional, Union, TypeVar, Generic, Callable
from datetime import datetime
import logging
from abc import ABC, abstractmethod

from app.models.domain.wishlist import Wishlist, WishlistItem
from app.models.database.dynamodb import DynamoDBWishlist, DynamoDBWishlistItem, DynamoDBWishlistMapper
from app.schemas.requests.wishlist import (
    CreateWishlistRequest, 
    UpdateWishlistRequest,
    GetWishlistsRequest
)
from app.schemas.responses.wishlist import (
    WishlistResponse,
    WishlistItemResponse,
    WishlistSummaryResponse,
    ProductDataResponse
)
from app.core.exceptions import ValidationError, TransformationError

logger = logging.getLogger(__name__)

T = TypeVar('T')
U = TypeVar('U')


class TransformationError(Exception):
    """Raised when data transformation fails."""
    pass


class DataTransformer(ABC, Generic[T, U]):
    """
    Abstract base class for data transformers.
    
    Provides a common interface for transforming data between different representations.
    """
    
    @abstractmethod
    def transform(self, source: T) -> U:
        """Transform source data to target format."""
        pass
    
    @abstractmethod
    def reverse_transform(self, target: U) -> T:
        """Transform target data back to source format."""
        pass
    
    def batch_transform(self, sources: List[T]) -> List[U]:
        """Transform a batch of source data."""
        return [self.transform(source) for source in sources]
    
    def batch_reverse_transform(self, targets: List[U]) -> List[T]:
        """Reverse transform a batch of target data."""
        return [self.reverse_transform(target) for target in targets]


class WishlistRequestTransformer(DataTransformer[CreateWishlistRequest, Wishlist]):
    """
    Transformer for converting wishlist requests to domain models.
    """
    
    def transform(self, request: CreateWishlistRequest) -> Wishlist:
        """Transform create request to domain wishlist."""
        try:
            return Wishlist.create_new(
                user_id=request.user_id,
                name=request.name,
                is_default=request.is_default,
                is_public=False  # New wishlists are private by default
            )
        except Exception as e:
            raise TransformationError(f"Failed to transform create request: {e}")
    
    def reverse_transform(self, wishlist: Wishlist) -> CreateWishlistRequest:
        """Transform domain wishlist to create request (for testing/validation)."""
        try:
            return CreateWishlistRequest(
                user_id=wishlist.user_id,
                name=wishlist.name,
                is_default=wishlist.is_default,
                country="ae",  # Default values
                language="en"
            )
        except Exception as e:
            raise TransformationError(f"Failed to reverse transform to create request: {e}")


class WishlistResponseTransformer(DataTransformer[Wishlist, WishlistResponse]):
    """
    Transformer for converting domain wishlists to API responses.
    """
    
    def __init__(self, include_items: bool = True, base_url: str = "https://api.example.com"):
        self.include_items = include_items
        self.base_url = base_url
    
    def transform(self, wishlist: Wishlist) -> WishlistResponse:
        """Transform domain wishlist to response format."""
        try:
            # Transform items if requested
            items = None
            if self.include_items:
                items = [self._transform_item(item) for item in wishlist.items]
            
            # Generate share URL
            share_url = f"{self.base_url}/api/v1/wishlists/shared/{wishlist.share_hash}"
            
            return WishlistResponse(
                user_id=wishlist.user_id,
                wishlist_id=wishlist.wishlist_id,
                name=wishlist.name,
                is_default=wishlist.is_default,
                is_public=wishlist.is_public,
                share_hash=wishlist.share_hash,
                share_url=share_url,
                item_count=len(wishlist.items),
                items=items,
                created_at=wishlist.created_at,
                updated_at=wishlist.updated_at
            )
        except Exception as e:
            raise TransformationError(f"Failed to transform wishlist to response: {e}")
    
    def reverse_transform(self, response: WishlistResponse) -> Wishlist:
        """Transform response back to domain wishlist (for testing)."""
        try:
            # Transform items
            items = []
            if response.items:
                items = [self._reverse_transform_item(item) for item in response.items]
            
            return Wishlist(
                user_id=response.user_id,
                wishlist_id=response.wishlist_id,
                name=response.name,
                is_default=response.is_default,
                is_public=response.is_public,
                share_hash=response.share_hash,
                items=items,
                created_at=response.created_at,
                updated_at=response.updated_at
            )
        except Exception as e:
            raise TransformationError(f"Failed to reverse transform response: {e}")
    
    def _transform_item(self, item: WishlistItem) -> WishlistItemResponse:
        """Transform domain item to response format."""
        product_data = None
        if item.product:
            product_data = ProductDataResponse(**item.product)
        
        return WishlistItemResponse(
            product_id=item.product_id,
            notes=item.notes,
            quantity=1,  # Default value
            priority=1,  # Default value
            added_at=item.added_at or datetime.utcnow(),
            updated_at=item.added_at or datetime.utcnow(),
            product=product_data
        )
    
    def _reverse_transform_item(self, item: WishlistItemResponse) -> WishlistItem:
        """Transform response item back to domain format."""
        product_data = None
        if item.product:
            product_data = item.product.dict() if hasattr(item.product, 'dict') else item.product
        
        return WishlistItem(
            product_id=item.product_id,
            notes=item.notes,
            added_at=item.added_at,
            product=product_data
        )


class WishlistSummaryTransformer(DataTransformer[Wishlist, WishlistSummaryResponse]):
    """
    Transformer for converting domain wishlists to summary responses (without items).
    """
    
    def transform(self, wishlist: Wishlist) -> WishlistSummaryResponse:
        """Transform domain wishlist to summary response."""
        try:
            return WishlistSummaryResponse(
                user_id=wishlist.user_id,
                wishlist_id=wishlist.wishlist_id,
                name=wishlist.name,
                is_default=wishlist.is_default,
                is_public=wishlist.is_public,
                item_count=len(wishlist.items),
                created_at=wishlist.created_at,
                updated_at=wishlist.updated_at
            )
        except Exception as e:
            raise TransformationError(f"Failed to transform wishlist to summary: {e}")
    
    def reverse_transform(self, summary: WishlistSummaryResponse) -> Wishlist:
        """Transform summary back to domain wishlist (partial)."""
        try:
            return Wishlist(
                user_id=summary.user_id,
                wishlist_id=summary.wishlist_id,
                name=summary.name,
                is_default=summary.is_default,
                is_public=summary.is_public,
                share_hash="",  # Not available in summary
                items=[],  # Not available in summary
                created_at=summary.created_at,
                updated_at=summary.updated_at
            )
        except Exception as e:
            raise TransformationError(f"Failed to reverse transform summary: {e}")


class ProductDataTransformer:
    """
    Transformer for external product data from various sources (Algolia, etc.).
    """
    
    @staticmethod
    def from_algolia_response(algolia_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Transform Algolia product data to standardized format.
        
        Args:
            algolia_data: Raw product data from Algolia
            
        Returns:
            Standardized product data dictionary
        """
        try:
            return {
                'product_id': algolia_data.get('objectID', ''),
                'title': algolia_data.get('title') or algolia_data.get('name', ''),
                'description': algolia_data.get('description', ''),
                'price': float(algolia_data.get('price', 0)) if algolia_data.get('price') else None,
                'currency': algolia_data.get('currency', 'AED'),
                'image_url': algolia_data.get('image_url') or algolia_data.get('image', ''),
                'brand': algolia_data.get('brand', ''),
                'category': algolia_data.get('category', ''),
                'availability': algolia_data.get('availability', 'unknown'),
                'rating': float(algolia_data.get('rating', 0)) if algolia_data.get('rating') else None,
                'review_count': int(algolia_data.get('review_count', 0)) if algolia_data.get('review_count') else None,
                'url': algolia_data.get('url', '')
            }
        except Exception as e:
            logger.error(f"Failed to transform Algolia data: {e}")
            # Return minimal data to prevent complete failure
            return {
                'product_id': algolia_data.get('objectID', ''),
                'title': algolia_data.get('title', 'Unknown Product'),
                'price': None,
                'currency': 'AED'
            }
    
    @staticmethod
    def batch_from_algolia(algolia_results: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        Transform batch of Algolia results to product data dictionary.
        
        Args:
            algolia_results: List of Algolia product data
            
        Returns:
            Dictionary mapping product_id to product data
        """
        products = {}
        for result in algolia_results:
            try:
                product_data = ProductDataTransformer.from_algolia_response(result)
                product_id = product_data.get('product_id')
                if product_id:
                    products[product_id] = product_data
            except Exception as e:
                logger.error(f"Failed to transform Algolia result: {e}")
                continue
        
        return products


class DataValidationPipeline:
    """
    Pipeline for validating and transforming data with comprehensive error reporting.
    """
    
    def __init__(self):
        self.validators: List[Callable[[Any], Any]] = []
        self.transformers: List[Callable[[Any], Any]] = []
        self.errors: List[str] = []
    
    def add_validator(self, validator: Callable[[Any], Any]) -> 'DataValidationPipeline':
        """Add a validation step to the pipeline."""
        self.validators.append(validator)
        return self
    
    def add_transformer(self, transformer: Callable[[Any], Any]) -> 'DataValidationPipeline':
        """Add a transformation step to the pipeline."""
        self.transformers.append(transformer)
        return self
    
    def process(self, data: Any) -> Any:
        """
        Process data through validation and transformation pipeline.
        
        Args:
            data: Input data to process
            
        Returns:
            Processed data
            
        Raises:
            ValidationError: If validation fails
            TransformationError: If transformation fails
        """
        self.errors = []
        current_data = data
        
        # Run validators
        for i, validator in enumerate(self.validators):
            try:
                current_data = validator(current_data)
            except Exception as e:
                self.errors.append(f"Validator {i+1}: {str(e)}")
        
        # Check for validation errors
        if self.errors:
            raise ValidationError(f"Validation failed: {'; '.join(self.errors)}")
        
        # Run transformers
        for i, transformer in enumerate(self.transformers):
            try:
                current_data = transformer(current_data)
            except Exception as e:
                raise TransformationError(f"Transformer {i+1} failed: {str(e)}")
        
        return current_data
    
    def get_errors(self) -> List[str]:
        """Get all validation errors."""
        return self.errors.copy()


class WishlistTransformationService:
    """
    High-level service for wishlist data transformations.
    
    Provides convenient methods for common transformation scenarios
    with proper error handling and logging.
    """
    
    def __init__(self, base_url: str = "https://api.example.com"):
        self.base_url = base_url
        self.request_transformer = WishlistRequestTransformer()
        self.response_transformer = WishlistResponseTransformer(base_url=base_url)
        self.summary_transformer = WishlistSummaryTransformer()
        self.db_mapper = DynamoDBWishlistMapper()
    
    def create_request_to_domain(self, request: CreateWishlistRequest) -> Wishlist:
        """Transform create request to domain model."""
        return self.request_transformer.transform(request)
    
    def update_request_to_domain(self, request: UpdateWishlistRequest, existing: Wishlist) -> Wishlist:
        """Apply update request to existing domain model."""
        try:
            # Create a copy of the existing wishlist
            updated = Wishlist(
                user_id=existing.user_id,
                wishlist_id=existing.wishlist_id,
                name=request.name if request.name is not None else existing.name,
                is_default=request.is_default if request.is_default is not None else existing.is_default,
                is_public=existing.is_public,
                share_hash=existing.share_hash,
                items=existing.items.copy(),
                created_at=existing.created_at,
                updated_at=datetime.utcnow()
            )
            
            return updated
        except Exception as e:
            raise TransformationError(f"Failed to apply update request: {e}")
    
    def domain_to_response(self, wishlist: Wishlist, include_items: bool = True) -> WishlistResponse:
        """Transform domain model to API response."""
        transformer = WishlistResponseTransformer(include_items=include_items, base_url=self.base_url)
        return transformer.transform(wishlist)
    
    def domain_to_summary(self, wishlist: Wishlist) -> WishlistSummaryResponse:
        """Transform domain model to summary response."""
        return self.summary_transformer.transform(wishlist)
    
    def domain_to_database(self, wishlist: Wishlist) -> Dict[str, Any]:
        """Transform domain model to database format."""
        return self.db_mapper.serialize_for_dynamodb(wishlist)
    
    def database_to_domain(self, db_item: Dict[str, Any]) -> Wishlist:
        """Transform database item to domain model."""
        return self.db_mapper.deserialize_from_dynamodb(db_item)
    
    def enrich_with_products(self, wishlist: Wishlist, product_data: Dict[str, Dict[str, Any]]) -> Wishlist:
        """
        Enrich wishlist items with product data.
        
        Args:
            wishlist: Domain wishlist model
            product_data: Dictionary mapping product_id to product data
            
        Returns:
            Wishlist with enriched items
        """
        try:
            enriched_items = []
            for item in wishlist.items:
                enriched_item = WishlistItem(
                    product_id=item.product_id,
                    notes=item.notes,
                    added_at=item.added_at,
                    product=product_data.get(item.product_id)
                )
                enriched_items.append(enriched_item)
            
            return Wishlist(
                user_id=wishlist.user_id,
                wishlist_id=wishlist.wishlist_id,
                name=wishlist.name,
                is_default=wishlist.is_default,
                is_public=wishlist.is_public,
                share_hash=wishlist.share_hash,
                items=enriched_items,
                created_at=wishlist.created_at,
                updated_at=wishlist.updated_at
            )
        except Exception as e:
            raise TransformationError(f"Failed to enrich wishlist with products: {e}")
    
    def batch_domain_to_response(
        self, 
        wishlists: List[Wishlist], 
        include_items: bool = True
    ) -> List[WishlistResponse]:
        """Transform multiple domain models to API responses."""
        transformer = WishlistResponseTransformer(include_items=include_items, base_url=self.base_url)
        return transformer.batch_transform(wishlists)
    
    def batch_domain_to_summary(self, wishlists: List[Wishlist]) -> List[WishlistSummaryResponse]:
        """Transform multiple domain models to summary responses."""
        return self.summary_transformer.batch_transform(wishlists)
    
    def validate_and_transform_create_request(self, request: CreateWishlistRequest) -> Wishlist:
        """Validate and transform create request with comprehensive error handling."""
        pipeline = DataValidationPipeline()
        
        # Add validators
        pipeline.add_validator(self._validate_create_request)
        pipeline.add_transformer(self.request_transformer.transform)
        
        return pipeline.process(request)
    
    def _validate_create_request(self, request: CreateWishlistRequest) -> CreateWishlistRequest:
        """Validate create request data."""
        if not request.user_id or not request.user_id.strip():
            raise ValidationError("user_id is required")
        
        if not request.name or not request.name.strip():
            raise ValidationError("name is required")
        
        if len(request.name) > 255:
            raise ValidationError("name cannot exceed 255 characters")
        
        return request