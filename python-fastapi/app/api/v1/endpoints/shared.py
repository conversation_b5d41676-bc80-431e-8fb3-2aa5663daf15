"""
Shared wishlist endpoints.

This module contains REST API endpoints for accessing public shared wishlists
using their share hash, without requiring authentication.
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path

from app.services.wishlist_service import WishlistService
from app.schemas.responses.wishlist import WishlistResponse
from app.schemas.responses.common import ErrorResponseSchema
from app.api.dependencies import (
    get_wishlist_service,
    validate_country_language
)
from app.utils.helpers import generate_correlation_id
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get(
    "/{share_hash}",
    response_model=WishlistResponse,
    summary="Get shared wishlist",
    description="Retrieve a public shared wishlist using its share hash"
)
async def get_shared_wishlist(
    share_hash: str = Path(..., description="The share hash of the wishlist"),
    country: str = Query("ae", description="Country code for product localization"),
    language: str = Query("en", description="Language code for product localization"),
    wishlist_service: WishlistService = Depends(get_wishlist_service),
    _: dict = Depends(validate_country_language)
) -> WishlistResponse:
    """Get a shared wishlist by its share hash."""
    correlation_id = generate_correlation_id()

    try:
        logger.info(f"Getting shared wishlist {share_hash}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash,
            "country": country,
            "language": language
        })

        wishlist = await wishlist_service.get_shared_wishlist(
            share_hash=share_hash,
            country=country,
            language=language
        )

        if not wishlist:
            logger.info(f"Shared wishlist not found or not public", extra={
                "correlation_id": correlation_id,
                "share_hash": share_hash
            })
            raise HTTPException(
                status_code=404,
                detail=ErrorResponseSchema(
                    code="SHARED_WISHLIST_NOT_FOUND",
                    message="Shared wishlist not found or is not public",
                    correlation_id=correlation_id
                ).dict()
            )

        return WishlistResponse.from_domain_model(wishlist)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get shared wishlist: {e}", extra={
            "correlation_id": correlation_id,
            "share_hash": share_hash,
            "error": str(e)
        })
        raise HTTPException(
            status_code=500,
            detail=ErrorResponseSchema(
                code="INTERNAL_ERROR",
                message="Failed to retrieve shared wishlist",
                correlation_id=correlation_id
            ).dict()
        )
