"""
Integration tests for circuit breaker with services.
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch

from app.utils.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerState,
    CircuitBreakerError,
    get_circuit_breaker_stats,
    reset_all_circuit_breakers
)


class TestCircuitBreakerIntegration:
    """Test circuit breaker integration with services."""
    
    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """Setup and teardown for each test."""
        # Reset all circuit breakers before each test
        reset_all_circuit_breakers()
        yield
        # Clean up after each test
        reset_all_circuit_breakers()
    
    @pytest.mark.asyncio
    async def test_algolia_service_circuit_breaker(self):
        """Test circuit breaker integration with Algolia service."""
        from app.services.algolia_service import AlgoliaService
        from app.repositories.cache_repo import CacheRepository
        
        # Mock cache repository
        cache_repo = AsyncMock(spec=CacheRepository)
        cache_repo.get.return_value = None  # No cached data
        
        # Create service instance
        service = AlgoliaService(cache_repo)
        
        # Mock the internal method to always fail
        async def failing_method(*args, **kwargs):
            raise Exception("Service unavailable")
        
        with patch.object(service, '_get_product_by_id_internal', side_effect=failing_method):
            # First few calls should fail and eventually open the circuit
            for i in range(6):  # More than failure threshold
                result = await service.get_product_by_id("test_product", "ae", "en")
                # Should return None or fallback result
                assert result is None or isinstance(result, dict)
        
        # Check that circuit breaker is now open
        stats = get_circuit_breaker_stats()
        algolia_stats = stats.get("algolia_service")
        if algolia_stats:
            # Circuit should be open after multiple failures
            assert algolia_stats["state"] in [CircuitBreakerState.OPEN.value, CircuitBreakerState.HALF_OPEN.value]
    
    @pytest.mark.asyncio
    async def test_cloudfront_service_circuit_breaker(self):
        """Test circuit breaker integration with CloudFront service."""
        from app.services.cloudfront_service import CloudFrontService
        
        # Create service instance
        service = CloudFrontService()
        
        # Mock the internal method to always fail
        async def failing_method(*args, **kwargs):
            raise Exception("CloudFront unavailable")
        
        with patch.object(service, '_create_invalidation', side_effect=failing_method):
            # Try to invalidate paths - should use fallback
            result = await service.invalidate_path("/test/path")
            # Should return True (queued) even if service fails
            assert isinstance(result, bool)
        
        # Check circuit breaker stats
        stats = get_circuit_breaker_stats()
        cloudfront_stats = stats.get("cloudfront_service")
        if cloudfront_stats:
            # Should have recorded some activity
            assert cloudfront_stats["total_requests"] >= 0
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_recovery(self):
        """Test circuit breaker recovery after service comes back online."""
        # Create a test circuit breaker
        breaker = CircuitBreaker(
            failure_threshold=2,
            recovery_timeout=0.1,  # Very short for testing
            name="test_recovery"
        )
        
        async def failing_func():
            raise ValueError("Service down")
        
        async def working_func():
            return "service_up"
        
        # Cause failures to open circuit
        for _ in range(2):
            with pytest.raises(ValueError):
                await breaker.call(failing_func)
        
        assert breaker.state == CircuitBreakerState.OPEN.value
        
        # Wait for recovery timeout
        await asyncio.sleep(0.2)
        
        # Next call should move to half-open and succeed
        result = await breaker.call(working_func)
        assert result == "service_up"
        assert breaker.state == CircuitBreakerState.CLOSED.value
    
    @pytest.mark.asyncio
    async def test_circuit_breaker_fallback_chain(self):
        """Test circuit breaker with fallback chain."""
        from app.utils.circuit_breaker import FallbackChain
        
        async def primary_fallback(*args, **kwargs):
            raise Exception("Primary fallback failed")
        
        async def secondary_fallback(*args, **kwargs):
            return "secondary_success"
        
        fallback_chain = FallbackChain(primary_fallback, secondary_fallback)
        
        breaker = CircuitBreaker(
            failure_threshold=1,
            recovery_timeout=1,
            name="test_fallback_chain",
            fallback_function=fallback_chain
        )
        
        async def failing_service():
            raise Exception("Service failed")
        
        # First call should fail and open circuit, but fallback will be used
        result = await breaker.call(failing_service)
        assert result == "secondary_success"  # Fallback chain should work
        
        # Circuit should be open now
        assert breaker.state == CircuitBreakerState.OPEN.value
        
        # Second call should also use fallback chain
        result2 = await breaker.call(failing_service)
        assert result2 == "secondary_success"
    
    @pytest.mark.asyncio
    async def test_multiple_circuit_breakers_stats(self):
        """Test statistics collection for multiple circuit breakers."""
        # Create multiple circuit breakers
        breaker1 = CircuitBreaker(name="service_1", failure_threshold=2)
        breaker2 = CircuitBreaker(name="service_2", failure_threshold=3)
        breaker3 = CircuitBreaker(name="service_3", failure_threshold=1)
        
        async def success_func():
            return "success"
        
        async def fail_func():
            raise Exception("fail")
        
        # Generate different patterns of success/failure
        await breaker1.call(success_func)
        await breaker2.call(success_func)
        
        # Make breaker3 fail and open
        with pytest.raises(Exception):
            await breaker3.call(fail_func)
        
        # Get comprehensive stats
        stats = get_circuit_breaker_stats()
        
        # Verify stats are collected for all breakers
        assert "service_1" in stats
        assert "service_2" in stats
        assert "service_3" in stats
        
        # Verify different states
        assert stats["service_1"]["state"] == CircuitBreakerState.CLOSED.value
        assert stats["service_2"]["state"] == CircuitBreakerState.CLOSED.value
        assert stats["service_3"]["state"] == CircuitBreakerState.OPEN.value
        
        # Verify success counts
        assert stats["service_1"]["success_count"] == 1
        assert stats["service_2"]["success_count"] == 1
        assert stats["service_3"]["success_count"] == 0
        
        # Verify failure counts
        assert stats["service_1"]["failure_count"] == 0
        assert stats["service_2"]["failure_count"] == 0
        assert stats["service_3"]["failure_count"] == 1