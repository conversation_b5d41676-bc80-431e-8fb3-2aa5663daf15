"""
DynamoDB client implementation with async support and connection pooling.
"""

import asyncio
import logging
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

import aioboto3
from botocore.config import Config
from botocore.exceptions import ClientError, BotoCoreError

from app.config.settings import get_settings
from app.core.exceptions import DatabaseError, ConnectionError

logger = logging.getLogger(__name__)


class DynamoDBClient:
    """Async DynamoDB client with connection pooling and health monitoring."""
    
    def __init__(self, settings=None):
        self.settings = settings or get_settings()
        self._session = None
        self._client = None
        self._resource = None
        self._connection_pool_size = 100
        self._max_retries = 3
        self._health_status = True
        
        # Configure boto3 with connection pooling
        self._config = Config(
            max_pool_connections=self._connection_pool_size,
            retries={
                'max_attempts': self._max_retries,
                'mode': 'adaptive'
            },
            read_timeout=30,
            connect_timeout=10,
            region_name=self.settings.DYNAMODB_REGION
        )
    
    async def initialize(self):
        """Initialize the DynamoDB client and session."""
        try:
            self._session = aioboto3.Session(
                aws_access_key_id=self.settings.AWS_ACCESS_KEY_ID,
                aws_secret_access_key=self.settings.AWS_SECRET_ACCESS_KEY,
                region_name=self.settings.DYNAMODB_REGION
            )
            
            # Test connection
            await self.health_check()
            logger.info("DynamoDB client initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize DynamoDB client: {e}")
            self._health_status = False
            raise ConnectionError(f"DynamoDB initialization failed: {e}")
    
    @asynccontextmanager
    async def get_client(self):
        """Get async DynamoDB client with automatic cleanup."""
        if not self._session:
            await self.initialize()
        
        async with self._session.client(
            'dynamodb',
            config=self._config,
            endpoint_url=self.settings.DYNAMODB_ENDPOINT
        ) as client:
            yield client
    
    @asynccontextmanager
    async def get_resource(self):
        """Get async DynamoDB resource with automatic cleanup."""
        if not self._session:
            await self.initialize()
        
        async with self._session.resource(
            'dynamodb',
            config=self._config,
            endpoint_url=self.settings.DYNAMODB_ENDPOINT
        ) as resource:
            yield resource
    
    async def health_check(self) -> bool:
        """Check DynamoDB connection health."""
        try:
            async with self.get_client() as client:
                # Simple operation to test connectivity
                await client.list_tables()
            
            self._health_status = True
            return True
            
        except Exception as e:
            logger.error(f"DynamoDB health check failed: {e}")
            self._health_status = False
            return False
    
    @property
    def is_healthy(self) -> bool:
        """Get current health status."""
        return self._health_status
    
    async def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection pool statistics."""
        return {
            "pool_size": self._connection_pool_size,
            "max_retries": self._max_retries,
            "healthy": self._health_status,
            "region": self.settings.DYNAMODB_REGION
        }


# Global DynamoDB client instance
_dynamodb_client: Optional[DynamoDBClient] = None


async def get_dynamodb_client() -> DynamoDBClient:
    """Get the global DynamoDB client instance."""
    global _dynamodb_client
    
    if _dynamodb_client is None:
        _dynamodb_client = DynamoDBClient()
        await _dynamodb_client.initialize()
    
    return _dynamodb_client


async def init_dynamodb(settings=None) -> DynamoDBClient:
    """Initialize DynamoDB client during application startup."""
    global _dynamodb_client
    
    _dynamodb_client = DynamoDBClient(settings)
    await _dynamodb_client.initialize()
    
    return _dynamodb_client


class DynamoDBOperations:
    """High-level DynamoDB operations with error handling and retries."""
    
    def __init__(self, client: DynamoDBClient):
        self.client = client
    
    async def get_item(
        self, 
        table_name: str, 
        key: Dict[str, Any],
        consistent_read: bool = False
    ) -> Optional[Dict[str, Any]]:
        """Get a single item from DynamoDB."""
        try:
            async with self.client.get_client() as dynamodb:
                response = await dynamodb.get_item(
                    TableName=table_name,
                    Key=key,
                    ConsistentRead=consistent_read
                )
                
                return response.get('Item')
                
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ResourceNotFoundException':
                logger.warning(f"Table {table_name} not found")
                return None
            else:
                logger.error(f"DynamoDB get_item error: {e}")
                raise DatabaseError(f"Failed to get item: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in get_item: {e}")
            raise DatabaseError(f"Unexpected database error: {e}")
    
    async def put_item(
        self, 
        table_name: str, 
        item: Dict[str, Any],
        condition_expression: Optional[str] = None
    ) -> bool:
        """Put an item into DynamoDB."""
        try:
            params = {
                'TableName': table_name,
                'Item': item
            }
            
            if condition_expression:
                params['ConditionExpression'] = condition_expression
            
            async with self.client.get_client() as dynamodb:
                await dynamodb.put_item(**params)
                return True
                
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ConditionalCheckFailedException':
                logger.warning(f"Conditional check failed for put_item")
                return False
            else:
                logger.error(f"DynamoDB put_item error: {e}")
                raise DatabaseError(f"Failed to put item: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in put_item: {e}")
            raise DatabaseError(f"Unexpected database error: {e}")
    
    async def update_item(
        self,
        table_name: str,
        key: Dict[str, Any],
        update_expression: str,
        expression_attribute_values: Optional[Dict[str, Any]] = None,
        expression_attribute_names: Optional[Dict[str, str]] = None,
        condition_expression: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """Update an item in DynamoDB."""
        try:
            params = {
                'TableName': table_name,
                'Key': key,
                'UpdateExpression': update_expression,
                'ReturnValues': 'ALL_NEW'
            }
            
            if expression_attribute_values:
                params['ExpressionAttributeValues'] = expression_attribute_values
            
            if expression_attribute_names:
                params['ExpressionAttributeNames'] = expression_attribute_names
            
            if condition_expression:
                params['ConditionExpression'] = condition_expression
            
            async with self.client.get_client() as dynamodb:
                response = await dynamodb.update_item(**params)
                return response.get('Attributes')
                
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ConditionalCheckFailedException':
                logger.warning(f"Conditional check failed for update_item")
                return None
            else:
                logger.error(f"DynamoDB update_item error: {e}")
                raise DatabaseError(f"Failed to update item: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in update_item: {e}")
            raise DatabaseError(f"Unexpected database error: {e}")
    
    async def delete_item(
        self,
        table_name: str,
        key: Dict[str, Any],
        condition_expression: Optional[str] = None
    ) -> bool:
        """Delete an item from DynamoDB."""
        try:
            params = {
                'TableName': table_name,
                'Key': key
            }
            
            if condition_expression:
                params['ConditionExpression'] = condition_expression
            
            async with self.client.get_client() as dynamodb:
                await dynamodb.delete_item(**params)
                return True
                
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == 'ConditionalCheckFailedException':
                logger.warning(f"Conditional check failed for delete_item")
                return False
            else:
                logger.error(f"DynamoDB delete_item error: {e}")
                raise DatabaseError(f"Failed to delete item: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in delete_item: {e}")
            raise DatabaseError(f"Unexpected database error: {e}")
    
    async def query(
        self,
        table_name: str,
        key_condition_expression: str,
        expression_attribute_values: Optional[Dict[str, Any]] = None,
        expression_attribute_names: Optional[Dict[str, str]] = None,
        index_name: Optional[str] = None,
        limit: Optional[int] = None,
        scan_index_forward: bool = True
    ) -> List[Dict[str, Any]]:
        """Query items from DynamoDB."""
        try:
            params = {
                'TableName': table_name,
                'KeyConditionExpression': key_condition_expression,
                'ScanIndexForward': scan_index_forward
            }
            
            if expression_attribute_values:
                params['ExpressionAttributeValues'] = expression_attribute_values
            
            if expression_attribute_names:
                params['ExpressionAttributeNames'] = expression_attribute_names
            
            if index_name:
                params['IndexName'] = index_name
            
            if limit:
                params['Limit'] = limit
            
            async with self.client.get_client() as dynamodb:
                response = await dynamodb.query(**params)
                return response.get('Items', [])
                
        except ClientError as e:
            logger.error(f"DynamoDB query error: {e}")
            raise DatabaseError(f"Failed to query items: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in query: {e}")
            raise DatabaseError(f"Unexpected database error: {e}")
    
    async def batch_get_item(
        self,
        request_items: Dict[str, Dict[str, Any]]
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Batch get items from DynamoDB."""
        try:
            async with self.client.get_client() as dynamodb:
                response = await dynamodb.batch_get_item(
                    RequestItems=request_items
                )
                return response.get('Responses', {})
                
        except ClientError as e:
            logger.error(f"DynamoDB batch_get_item error: {e}")
            raise DatabaseError(f"Failed to batch get items: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in batch_get_item: {e}")
            raise DatabaseError(f"Unexpected database error: {e}")