# Design Document - <PERSON><PERSON> to FastAPI Migration

## Overview

This document outlines the technical design for migrating the Mumzworld Wishlist Service from Laravel (PHP) to FastAPI (Python). The design emphasizes ultra-high performance, modern Python ecosystem practices, and AWS App Runner deployment while maintaining complete API compatibility.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[Web Applications]
        MOB[Mobile Applications]
        API_CLIENTS[API Clients]
    end
    
    subgraph "AWS Cloud"
        subgraph "CDN & Load Balancing"
            CF[CloudFront CDN]
            ALB[Application Load Balancer]
        end
        
        subgraph "AWS App Runner"
            AR1[FastAPI Instance 1]
            AR2[FastAPI Instance 2]
            AR3[FastAPI Instance N]
        end
        
        subgraph "Data Layer"
            DDB[(DynamoDB)]
            REDIS[(ElastiCache Redis)]
            MYSQL[(RDS MySQL)]
        end
        
        subgraph "External Services"
            ALGOLIA[Algolia Search]
            SQS[SQS Queues]
            CELERY[Celery Workers]
        end
        
        subgraph "Monitoring"
            CW[CloudWatch]
            XRAY[X-Ray Tracing]
            PROM[Prometheus Metrics]
        end
    end
    
    WEB --> CF
    MOB --> CF
    API_CLIENTS --> CF
    
    CF --> ALB
    ALB --> AR1
    ALB --> AR2
    ALB --> AR3
    
    AR1 --> DDB
    AR1 --> REDIS
    AR1 --> MYSQL
    AR1 --> ALGOLIA
    AR1 --> SQS
    
    SQS --> CELERY
    
    AR1 --> CW
    AR1 --> XRAY
    AR1 --> PROM
```

### FastAPI Application Architecture

```mermaid
graph TB
    subgraph "FastAPI Application"
        subgraph "API Layer"
            ROUTES[Route Handlers]
            MIDDLEWARE[Middleware Stack]
            DEPS[Dependencies]
        end
        
        subgraph "Business Layer"
            SERVICES[Service Classes]
            SCHEMAS[Pydantic Schemas]
            VALIDATORS[Custom Validators]
        end
        
        subgraph "Data Layer"
            REPOS[Repository Pattern]
            MODELS[Data Models]
            ADAPTERS[Database Adapters]
        end
        
        subgraph "Infrastructure"
            CACHE[Cache Manager]
            QUEUE[Queue Manager]
            METRICS[Metrics Collector]
            LOGGER[Structured Logger]
        end
    end
    
    ROUTES --> MIDDLEWARE
    MIDDLEWARE --> DEPS
    DEPS --> SERVICES
    SERVICES --> SCHEMAS
    SERVICES --> REPOS
    REPOS --> MODELS
    REPOS --> ADAPTERS
    
    SERVICES --> CACHE
    SERVICES --> QUEUE
    SERVICES --> METRICS
    SERVICES --> LOGGER
```

## Components and Interfaces

### 1. Project Structure

```
python-fastapi/
├── app/
│   ├── __init__.py
│   ├── main.py                    # FastAPI application entry point
│   ├── config/
│   │   ├── __init__.py
│   │   ├── settings.py            # Pydantic settings management
│   │   └── logging.py             # Logging configuration
│   ├── api/
│   │   ├── __init__.py
│   │   ├── dependencies.py        # Dependency injection
│   │   ├── middleware.py          # Custom middleware
│   │   └── v1/
│   │       ├── __init__.py
│   │       ├── router.py          # Main API router
│   │       └── endpoints/
│   │           ├── __init__.py
│   │           ├── wishlists.py   # Wishlist endpoints
│   │           ├── items.py       # Wishlist items endpoints
│   │           ├── shared.py      # Shared wishlists endpoints
│   │           └── health.py      # Health check endpoints
│   ├── core/
│   │   ├── __init__.py
│   │   ├── exceptions.py          # Custom exceptions
│   │   ├── security.py            # Authentication & authorization
│   │   ├── metrics.py             # Prometheus metrics
│   │   └── tracing.py             # Distributed tracing
│   ├── services/
│   │   ├── __init__.py
│   │   ├── wishlist_service.py    # Core business logic
│   │   ├── algolia_service.py     # Product search integration
│   │   ├── cache_service.py       # Caching abstraction
│   │   └── notification_service.py # Background notifications
│   ├── repositories/
│   │   ├── __init__.py
│   │   ├── base.py                # Base repository interface
│   │   ├── wishlist_repo.py       # DynamoDB wishlist operations
│   │   └── cache_repo.py          # Redis cache operations
│   ├── models/
│   │   ├── __init__.py
│   │   ├── domain/                # Domain models
│   │   │   ├── __init__.py
│   │   │   ├── wishlist.py        # Wishlist domain model
│   │   │   └── item.py            # Wishlist item domain model
│   │   └── database/              # Database models
│   │       ├── __init__.py
│   │       └── dynamodb.py        # DynamoDB model definitions
│   ├── schemas/
│   │   ├── __init__.py
│   │   ├── requests/              # Request schemas
│   │   │   ├── __init__.py
│   │   │   ├── wishlist.py        # Wishlist request schemas
│   │   │   └── item.py            # Item request schemas
│   │   └── responses/             # Response schemas
│   │       ├── __init__.py
│   │       ├── wishlist.py        # Wishlist response schemas
│   │       ├── item.py            # Item response schemas
│   │       └── common.py          # Common response schemas
│   ├── infrastructure/
│   │   ├── __init__.py
│   │   ├── database/
│   │   │   ├── __init__.py
│   │   │   ├── dynamodb.py        # DynamoDB connection
│   │   │   ├── redis.py           # Redis connection
│   │   │   └── mysql.py           # MySQL connection (if needed)
│   │   ├── external/
│   │   │   ├── __init__.py
│   │   │   ├── algolia.py         # Algolia client
│   │   │   └── cloudfront.py      # CloudFront client
│   │   └── queue/
│   │       ├── __init__.py
│   │       ├── celery_app.py      # Celery configuration
│   │       └── tasks.py           # Background tasks
│   └── utils/
│       ├── __init__.py
│       ├── helpers.py             # Utility functions
│       ├── decorators.py          # Custom decorators
│       └── constants.py           # Application constants
├── tests/
│   ├── __init__.py
│   ├── conftest.py                # Pytest configuration
│   ├── unit/                      # Unit tests
│   ├── integration/               # Integration tests
│   └── performance/               # Performance tests
├── scripts/
│   ├── migrate_data.py            # Data migration scripts
│   ├── validate_data.py           # Data validation scripts
│   └── load_test.py               # Load testing scripts
├── docker/
│   ├── Dockerfile                 # Production Docker image
│   ├── Dockerfile.dev             # Development Docker image
│   └── docker-compose.yml         # Local development setup
├── deployment/
│   ├── apprunner.yaml             # App Runner configuration
│   ├── cloudformation/            # Infrastructure as Code
│   └── kubernetes/                # K8s manifests (if needed)
├── requirements/
│   ├── base.txt                   # Base requirements
│   ├── dev.txt                    # Development requirements
│   └── prod.txt                   # Production requirements
├── pyproject.toml                 # Poetry configuration
├── pytest.ini                    # Pytest configuration
├── mypy.ini                       # Type checking configuration
├── .pre-commit-config.yaml        # Pre-commit hooks
└── README.md                      # Project documentation
```

### 2. Core Components Design

#### FastAPI Application (main.py)

```python
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
import uvicorn
from contextlib import asynccontextmanager

from app.api.v1.router import api_router
from app.api.middleware import (
    RequestLoggingMiddleware,
    MetricsMiddleware,
    RateLimitMiddleware
)
from app.config.settings import get_settings
from app.infrastructure.database.dynamodb import init_dynamodb
from app.infrastructure.database.redis import init_redis
from app.core.metrics import init_metrics

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    settings = get_settings()
    await init_dynamodb(settings)
    await init_redis(settings)
    init_metrics()
    yield
    # Shutdown
    # Cleanup connections

def create_app() -> FastAPI:
    settings = get_settings()
    
    app = FastAPI(
        title="Mumzworld Wishlist Service",
        description="High-performance wishlist microservice",
        version="2.0.0",
        lifespan=lifespan,
        docs_url="/docs" if settings.DEBUG else None,
        redoc_url="/redoc" if settings.DEBUG else None,
    )
    
    # Middleware stack (order matters)
    app.add_middleware(GZipMiddleware, minimum_size=1000)
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.ALLOWED_ORIGINS,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    app.add_middleware(RequestLoggingMiddleware)
    app.add_middleware(MetricsMiddleware)
    app.add_middleware(RateLimitMiddleware)
    
    # Include routers
    app.include_router(api_router, prefix="/api/v1")
    
    return app

app = create_app()

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        workers=1,  # App Runner handles scaling
        loop="uvloop",  # High-performance event loop
        http="httptools",  # High-performance HTTP parser
    )
```

#### Settings Management (config/settings.py)

```python
from pydantic_settings import BaseSettings
from pydantic import Field, validator
from typing import List, Optional
from functools import lru_cache

class Settings(BaseSettings):
    # Application
    APP_NAME: str = "Mumzworld Wishlist Service"
    DEBUG: bool = False
    VERSION: str = "2.0.0"
    
    # API Configuration
    API_V1_PREFIX: str = "/api/v1"
    ALLOWED_ORIGINS: List[str] = ["*"]
    
    # Database
    DYNAMODB_ENDPOINT: Optional[str] = None
    DYNAMODB_REGION: str = "us-east-1"
    AWS_ACCESS_KEY_ID: Optional[str] = None
    AWS_SECRET_ACCESS_KEY: Optional[str] = None
    
    # Redis
    REDIS_URL: str = Field(..., env="REDIS_URL")
    REDIS_MAX_CONNECTIONS: int = 100
    REDIS_RETRY_ON_TIMEOUT: bool = True
    
    # Algolia
    ALGOLIA_APP_ID: str = Field(..., env="ALGOLIA_APP_ID")
    ALGOLIA_API_KEY: str = Field(..., env="ALGOLIA_API_KEY")
    ALGOLIA_TIMEOUT: int = 5
    ALGOLIA_MAX_RETRIES: int = 3
    
    # CloudFront
    CLOUDFRONT_DISTRIBUTION_ID: Optional[str] = None
    CLOUDFRONT_KEY_PAIR_ID: Optional[str] = None
    
    # Performance
    MAX_CONCURRENT_REQUESTS: int = 1000
    REQUEST_TIMEOUT: int = 30
    CACHE_TTL_DEFAULT: int = 300  # 5 minutes
    CACHE_TTL_PRODUCTS: int = 3600  # 1 hour
    
    # Monitoring
    ENABLE_METRICS: bool = True
    ENABLE_TRACING: bool = True
    LOG_LEVEL: str = "INFO"
    
    # Rate Limiting
    RATE_LIMIT_REQUESTS: int = 1000
    RATE_LIMIT_WINDOW: int = 60  # seconds
    
    class Config:
        env_file = ".env"
        case_sensitive = True

@lru_cache()
def get_settings() -> Settings:
    return Settings()
```

#### Wishlist Service (services/wishlist_service.py)

```python
from typing import List, Optional, Dict, Any
import asyncio
from uuid import uuid4
from datetime import datetime

from app.repositories.wishlist_repo import WishlistRepository
from app.repositories.cache_repo import CacheRepository
from app.services.algolia_service import AlgoliaService
from app.services.notification_service import NotificationService
from app.models.domain.wishlist import Wishlist, WishlistItem
from app.schemas.requests.wishlist import CreateWishlistRequest, UpdateWishlistRequest
from app.core.exceptions import WishlistNotFoundError, ValidationError
from app.core.metrics import metrics
from app.utils.helpers import generate_share_hash

class WishlistService:
    def __init__(
        self,
        wishlist_repo: WishlistRepository,
        cache_repo: CacheRepository,
        algolia_service: AlgoliaService,
        notification_service: NotificationService,
    ):
        self.wishlist_repo = wishlist_repo
        self.cache_repo = cache_repo
        self.algolia_service = algolia_service
        self.notification_service = notification_service

    async def create_wishlist(
        self, 
        user_id: str, 
        request: CreateWishlistRequest
    ) -> Wishlist:
        """Create a new wishlist for a user."""
        with metrics.timer("wishlist_create_duration"):
            # Handle default wishlist logic
            if request.is_default:
                await self._unset_default_wishlist(user_id)
            
            # Create new wishlist
            wishlist = Wishlist(
                user_id=user_id,
                wishlist_id=str(uuid4()),
                name=request.name,
                is_default=request.is_default,
                is_public=False,
                share_hash=generate_share_hash(),
                items=[],
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )
            
            # Save to database
            await self.wishlist_repo.create(wishlist)
            
            # Invalidate cache
            await self._invalidate_user_cache(user_id)
            
            # Send notification (async)
            asyncio.create_task(
                self.notification_service.notify_wishlist_created(wishlist)
            )
            
            metrics.counter("wishlist_created").inc()
            return wishlist

    async def get_user_wishlists(
        self, 
        user_id: str, 
        country: str = "ae", 
        language: str = "en"
    ) -> List[Wishlist]:
        """Get all wishlists for a user with product enrichment."""
        cache_key = f"user_wishlists:{user_id}:{country}:{language}"
        
        # Try cache first
        cached_result = await self.cache_repo.get(cache_key)
        if cached_result:
            metrics.counter("cache_hit", {"type": "user_wishlists"}).inc()
            return cached_result
        
        with metrics.timer("get_user_wishlists_duration"):
            # Get from database
            wishlists = await self.wishlist_repo.get_by_user_id(user_id)
            
            # Enrich with product details concurrently
            enriched_wishlists = await self._enrich_wishlists_with_products(
                wishlists, country, language
            )
            
            # Cache the result
            await self.cache_repo.set(
                cache_key, 
                enriched_wishlists, 
                ttl=300  # 5 minutes
            )
            
            metrics.counter("cache_miss", {"type": "user_wishlists"}).inc()
            return enriched_wishlists

    async def get_wishlist_by_id(
        self, 
        user_id: str, 
        wishlist_id: str,
        country: str = "ae",
        language: str = "en"
    ) -> Optional[Wishlist]:
        """Get a specific wishlist by ID."""
        cache_key = f"wishlist:{user_id}:{wishlist_id}:{country}:{language}"
        
        # Try cache first
        cached_result = await self.cache_repo.get(cache_key)
        if cached_result:
            return cached_result
        
        # Get from database
        wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
        if not wishlist:
            return None
        
        # Enrich with product details
        enriched_wishlist = await self._enrich_wishlist_with_products(
            wishlist, country, language
        )
        
        # Cache the result
        await self.cache_repo.set(cache_key, enriched_wishlist, ttl=300)
        
        return enriched_wishlist

    async def add_item_to_wishlist(
        self, 
        user_id: str, 
        wishlist_id: str, 
        product_id: str,
        notes: Optional[str] = None
    ) -> Wishlist:
        """Add an item to a wishlist."""
        # Get wishlist
        wishlist = await self.wishlist_repo.get_by_id(user_id, wishlist_id)
        if not wishlist:
            raise WishlistNotFoundError(f"Wishlist {wishlist_id} not found")
        
        # Create new item
        item = WishlistItem(
            product_id=product_id,
            notes=notes,
            added_at=datetime.utcnow()
        )
        
        # Add item (replace if exists)
        existing_items = {item.product_id: item for item in wishlist.items}
        existing_items[product_id] = item
        wishlist.items = list(existing_items.values())
        wishlist.updated_at = datetime.utcnow()
        
        # Save to database
        await self.wishlist_repo.update(wishlist)
        
        # Invalidate cache
        await self._invalidate_wishlist_cache(user_id, wishlist_id)
        
        return wishlist

    async def _enrich_wishlists_with_products(
        self, 
        wishlists: List[Wishlist], 
        country: str, 
        language: str
    ) -> List[Wishlist]:
        """Enrich multiple wishlists with product data concurrently."""
        tasks = [
            self._enrich_wishlist_with_products(wishlist, country, language)
            for wishlist in wishlists
        ]
        return await asyncio.gather(*tasks)

    async def _enrich_wishlist_with_products(
        self, 
        wishlist: Wishlist, 
        country: str, 
        language: str
    ) -> Wishlist:
        """Enrich a single wishlist with product data."""
        if not wishlist.items:
            return wishlist
        
        # Extract product IDs
        product_ids = [item.product_id for item in wishlist.items]
        
        # Get product data from Algolia
        products = await self.algolia_service.get_products_by_ids(
            product_ids, country, language
        )
        
        # Enrich items with product data
        for item in wishlist.items:
            if item.product_id in products:
                item.product = products[item.product_id]
        
        return wishlist

    async def _unset_default_wishlist(self, user_id: str) -> None:
        """Unset any existing default wishlist for a user."""
        current_default = await self.wishlist_repo.get_default_wishlist(user_id)
        if current_default:
            current_default.is_default = False
            current_default.updated_at = datetime.utcnow()
            await self.wishlist_repo.update(current_default)

    async def _invalidate_user_cache(self, user_id: str) -> None:
        """Invalidate all cache entries for a user."""
        pattern = f"user_wishlists:{user_id}:*"
        await self.cache_repo.delete_pattern(pattern)

    async def _invalidate_wishlist_cache(self, user_id: str, wishlist_id: str) -> None:
        """Invalidate cache entries for a specific wishlist."""
        patterns = [
            f"wishlist:{user_id}:{wishlist_id}:*",
            f"user_wishlists:{user_id}:*"
        ]
        for pattern in patterns:
            await self.cache_repo.delete_pattern(pattern)
```

## Data Models

### Domain Models

```python
# app/models/domain/wishlist.py
from dataclasses import dataclass
from datetime import datetime
from typing import List, Optional, Dict, Any
from uuid import UUID

@dataclass
class WishlistItem:
    product_id: str
    notes: Optional[str] = None
    added_at: Optional[datetime] = None
    product: Optional[Dict[str, Any]] = None  # Enriched product data

@dataclass
class Wishlist:
    user_id: str
    wishlist_id: str
    name: str
    is_default: bool
    is_public: bool
    share_hash: str
    items: List[WishlistItem]
    created_at: datetime
    updated_at: datetime
    
    def add_item(self, product_id: str, notes: Optional[str] = None) -> None:
        """Add or update an item in the wishlist."""
        # Remove existing item if present
        self.items = [item for item in self.items if item.product_id != product_id]
        
        # Add new item
        new_item = WishlistItem(
            product_id=product_id,
            notes=notes,
            added_at=datetime.utcnow()
        )
        self.items.append(new_item)
        self.updated_at = datetime.utcnow()
    
    def remove_item(self, product_id: str) -> bool:
        """Remove an item from the wishlist."""
        original_count = len(self.items)
        self.items = [item for item in self.items if item.product_id != product_id]
        
        if len(self.items) < original_count:
            self.updated_at = datetime.utcnow()
            return True
        return False
```

### Pydantic Schemas

```python
# app/schemas/requests/wishlist.py
from pydantic import BaseModel, Field, validator
from typing import Optional

class CreateWishlistRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    is_default: bool = False

class UpdateWishlistRequest(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    is_default: Optional[bool] = None

class AddItemRequest(BaseModel):
    product_id: str = Field(..., min_length=1)
    notes: Optional[str] = Field(None, max_length=500)

# app/schemas/responses/wishlist.py
from pydantic import BaseModel
from datetime import datetime
from typing import List, Optional, Dict, Any

class WishlistItemResponse(BaseModel):
    product_id: str
    notes: Optional[str]
    added_at: datetime
    product: Optional[Dict[str, Any]] = None

class WishlistResponse(BaseModel):
    user_id: str
    wishlist_id: str
    name: str
    is_default: bool
    is_public: bool
    share_hash: str
    items: List[WishlistItemResponse]
    created_at: datetime
    updated_at: datetime

class WishlistListResponse(BaseModel):
    wishlists: List[WishlistResponse]
    total: int
```

## Error Handling

### Custom Exceptions

```python
# app/core/exceptions.py
from fastapi import HTTPException
from typing import Any, Dict, Optional

class WishlistServiceException(Exception):
    """Base exception for wishlist service."""
    pass

class WishlistNotFoundError(WishlistServiceException):
    """Raised when a wishlist is not found."""
    pass

class ValidationError(WishlistServiceException):
    """Raised when validation fails."""
    pass

class ExternalServiceError(WishlistServiceException):
    """Raised when external service fails."""
    pass

# Exception handlers
from fastapi import Request
from fastapi.responses import JSONResponse

async def wishlist_not_found_handler(request: Request, exc: WishlistNotFoundError):
    return JSONResponse(
        status_code=404,
        content={
            "error": {
                "code": "WISHLIST_NOT_FOUND",
                "message": str(exc),
                "request_id": request.headers.get("X-Request-ID")
            }
        }
    )

async def validation_error_handler(request: Request, exc: ValidationError):
    return JSONResponse(
        status_code=400,
        content={
            "error": {
                "code": "VALIDATION_ERROR",
                "message": str(exc),
                "request_id": request.headers.get("X-Request-ID")
            }
        }
    )
```

## Testing Strategy

### Test Structure

```python
# tests/conftest.py
import pytest
import asyncio
from httpx import AsyncClient
from app.main import create_app
from app.config.settings import get_settings

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def app():
    """Create FastAPI app for testing."""
    return create_app()

@pytest.fixture
async def client(app):
    """Create async HTTP client for testing."""
    async with AsyncClient(app=app, base_url="http://test") as ac:
        yield ac

@pytest.fixture
def mock_dynamodb():
    """Mock DynamoDB for testing."""
    # Implementation for mocking DynamoDB
    pass

# tests/unit/services/test_wishlist_service.py
import pytest
from unittest.mock import AsyncMock, Mock
from app.services.wishlist_service import WishlistService
from app.models.domain.wishlist import Wishlist

@pytest.mark.asyncio
async def test_create_wishlist():
    # Mock dependencies
    wishlist_repo = AsyncMock()
    cache_repo = AsyncMock()
    algolia_service = AsyncMock()
    notification_service = AsyncMock()
    
    service = WishlistService(
        wishlist_repo, cache_repo, algolia_service, notification_service
    )
    
    # Test wishlist creation
    request = CreateWishlistRequest(name="Test Wishlist", is_default=True)
    result = await service.create_wishlist("user123", request)
    
    assert result.name == "Test Wishlist"
    assert result.is_default is True
    wishlist_repo.create.assert_called_once()
```

## Performance Optimizations

### 1. Async/Await Implementation

```python
# All I/O operations use async/await
async def get_user_wishlists(self, user_id: str) -> List[Wishlist]:
    # Concurrent database and cache operations
    cache_task = asyncio.create_task(self.cache_repo.get(cache_key))
    db_task = asyncio.create_task(self.wishlist_repo.get_by_user_id(user_id))
    
    cached_result = await cache_task
    if cached_result:
        return cached_result
    
    wishlists = await db_task
    # Process results...
```

### 2. Connection Pooling

```python
# app/infrastructure/database/dynamodb.py
import aioboto3
from botocore.config import Config

class DynamoDBClient:
    def __init__(self, settings):
        self.config = Config(
            max_pool_connections=100,
            retries={'max_attempts': 3}
        )
        self.session = aioboto3.Session()
    
    async def get_client(self):
        return self.session.client(
            'dynamodb',
            config=self.config,
            region_name=settings.DYNAMODB_REGION
        )
```

### 3. Caching Strategy

```python
# Multi-tier caching implementation
class CacheService:
    def __init__(self):
        self.l1_cache = {}  # In-memory cache
        self.l2_cache = redis_client  # Redis cache
    
    async def get(self, key: str):
        # L1 cache (in-memory)
        if key in self.l1_cache:
            return self.l1_cache[key]
        
        # L2 cache (Redis)
        value = await self.l2_cache.get(key)
        if value:
            self.l1_cache[key] = value  # Populate L1
            return value
        
        return None
```

## Deployment Architecture

### AWS App Runner Configuration

```yaml
# deployment/apprunner.yaml
version: 1.0
runtime: python3.11
build:
  commands:
    build:
      - pip install --upgrade pip
      - pip install -r requirements/prod.txt
run:
  runtime-version: 3.11
  command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 1
  network:
    port: 8000
    env: PORT
  env:
    - name: PYTHONPATH
      value: /app
    - name: ENVIRONMENT
      value: production
```

### Docker Configuration

```dockerfile
# docker/Dockerfile
FROM python:3.11-slim as builder

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements/prod.txt .
RUN pip install --no-cache-dir -r prod.txt

FROM python:3.11-slim as runtime

# Copy installed packages
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Create app user
RUN useradd --create-home --shell /bin/bash app

# Set working directory
WORKDIR /app

# Copy application code
COPY app/ ./app/
COPY scripts/ ./scripts/

# Set ownership
RUN chown -R app:app /app
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run application
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

This design provides a comprehensive foundation for building a high-performance FastAPI service that maintains compatibility with the existing Laravel service while leveraging modern Python ecosystem advantages and AWS App Runner's serverless scaling capabilities.